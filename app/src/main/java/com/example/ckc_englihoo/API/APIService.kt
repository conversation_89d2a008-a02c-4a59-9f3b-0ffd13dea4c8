package com.example.ckc_englihoo.API

import com.example.ckc_englihoo.DataClass.*
import retrofit2.Response
import retrofit2.http.*

interface APIService {
    // ==================== AUTHENTICATION ====================
    @GET("StudentDN/{taikhoan}/{matkhau}")
    suspend fun kiemTraDangNhap(
        @Path("taikhoan") taikhoan: String,
        @Path("matkhau") matkhau: String
    ): Response<List<Student>>

    @GET("TeacherDN/{taikhoan}/{matkhau}")
    suspend fun kiemTraDangNhapTeacher(
        @Path("taikhoan") taikhoan: String,
        @Path("matkhau") matkhau: String
    ): Response<List<Teacher>>

    // ==================== STUDENT MANAGEMENT ====================
    @PUT("students/{studentId}/change-password")
    suspend fun changePassword(
        @Path("studentId") studentId: Int,
        @Body request: Map<String, String>
    ): Response<Map<String, Any>>

    @GET("students/{studentId}")
    suspend fun getStudentById(@Path("studentId") studentId: Int): Response<Student>

    @GET("students")
    suspend fun getAllStudents(): Response<List<Student>>

    @PUT("students/{studentId}")
    suspend fun updateStudent(
        @Path("studentId") studentId: Int,
        @Body request: Map<String, Any>
    ): Response<Map<String, Any>>

    // ==================== COURSE MANAGEMENT ====================
    @GET("courses/{courseId}/students/count")
    suspend fun getCourseStudentCount(@Path("courseId") courseId: Int): Response<Map<String, Any>>

    @GET("courses")
    suspend fun getAllCourses(): Response<List<Course>>

    @GET("courses/{courseId}")
    suspend fun getCourseById(@Path("courseId") courseId: Int): Response<Course>

    @GET("courses/student/{studentId}")
    suspend fun getCoursesByStudentId(@Path("studentId") studentId: Int): Response<List<Course>>

    @GET("courses/level/{level}")
    suspend fun getCoursesByLevel(@Path("level") level: String): Response<List<Course>>

    // ==================== COURSE ENROLLMENT ====================
    @POST("enrollments")
    suspend fun enrollStudent(@Body enrollment: Map<String, Any>): Response<Map<String, Any>>

    @GET("enrollments/course/{courseId}")
    suspend fun getEnrollmentsByCourseId(@Path("courseId") courseId: Int): Response<List<CourseEnrollment>>

    @POST("enrollments/smart-register/student/{studentId}")
    suspend fun smartCourseRegistration(
        @Path("studentId") studentId: Int,
        @Body request: Map<String, String>
    ): Response<Map<String, Any>>

    @GET("enrollments/student/{studentId}")
    suspend fun getStudentEnrollments(@Path("studentId") studentId: Int): Response<List<CourseEnrollment>>

    @PUT("enrollments/{enrollmentId}/status")
    suspend fun updateEnrollmentStatus(
        @Path("enrollmentId") enrollmentId: Int,
        @Body status: Map<String, Any>
    ): Response<Map<String, Any>>

    // ==================== TEACHER MANAGEMENT ====================
    @GET("teachers")
    suspend fun getAllTeachers(): Response<List<Teacher>>

    @GET("teachers/{teacherId}")
    suspend fun getTeacherById(@Path("teacherId") teacherId: Int): Response<Teacher>

    @GET("teachers/course/{courseId}")
    suspend fun getTeachersByCourseId(@Path("courseId") courseId: Int): Response<List<Teacher>>

    // ==================== LESSON MANAGEMENT ====================
    @GET("lessons/course/{courseId}")
    suspend fun getLessonsByCourseId(@Path("courseId") courseId: Int): Response<List<Lesson>>

    @GET("lessons/{level}")
    suspend fun getLessonByLevel(@Path("level") level: String): Response<Lesson>

    // ==================== LESSON PARTS ====================
    @GET("lesson-parts/lesson/{level}")
    suspend fun getLessonPartsByLevel(@Path("level") level: String): Response<List<LessonPart>>

    @GET("lesson-parts/{lessonPartId}")
    suspend fun getLessonPartById(@Path("lessonPartId") lessonPartId: Int): Response<LessonPart>

    @GET("lesson-part-questions/{lessonPartId}")
    suspend fun getLessonPartQuestions(@Path("lessonPartId") lessonPartId: Int): Response<List<Question>>

    @GET("lesson-parts/course/{courseId}")
    suspend fun getLessonPartsByCourse(@Path("courseId") courseId: Int): Response<List<LessonPart>>

    @GET("lesson-parts/{lessonPartId}/details")
    suspend fun getLessonPartDetails(@Path("lessonPartId") lessonPartId: Int): Response<LessonPart>

    @GET("lesson-parts/{lessonPartId}/student/{studentId}/progress")
    suspend fun getLessonPartProgressByStudent(
        @Path("lessonPartId") lessonPartId: Int,
        @Path("studentId") studentId: Int
    ): Response<LessonPartProgress>

    // ==================== SCORES & PROGRESS ====================
    @GET("scores/student/{studentId}")
    suspend fun getScoresByStudentId(@Path("studentId") studentId: Int): Response<List<LessonPartScore>>

    @GET("scores/lesson-part/{lessonPartId}/student/{studentId}")
    suspend fun getScoreByLessonPartAndStudent(
        @Path("lessonPartId") lessonPartId: Int,
        @Path("studentId") studentId: Int
    ): Response<LessonPartScore>

    @POST("scores")
    suspend fun submitScore(@Body score: Map<String, Any>): Response<Map<String, Any>>

    // ==================== PROGRESS TRACKING ====================
    @GET("progress/course/{courseId}/student/{studentId}")
    suspend fun getCourseProgress(
        @Path("courseId") courseId: Int,
        @Path("studentId") studentId: Int
    ): Response<CourseProgress>

    @GET("progress/student/{studentId}/overview")
    suspend fun getStudentOverallProgress(@Path("studentId") studentId: Int): Response<OverallProgress>

    @GET("progress/lesson-part/{lessonPartId}/student/{studentId}")
    suspend fun getLessonPartProgress(
        @Path("lessonPartId") lessonPartId: Int,
        @Path("studentId") studentId: Int
    ): Response<LessonPartProgress>

    @GET("progress/lesson-part/{lessonPartId}/student/{studentId}/course/{courseId}")
    suspend fun getLessonPartProgressWithCourse(
        @Path("lessonPartId") lessonPartId: Int,
        @Path("studentId") studentId: Int,
        @Path("courseId") courseId: Int
    ): Response<LessonPartProgress>

    @GET("progress/lesson/{lessonLevel}/student/{studentId}")
    suspend fun getLessonProgress(
        @Path("lessonLevel") lessonLevel: String,
        @Path("studentId") studentId: Int
    ): Response<LessonProgress>

    @GET("progress/lesson/{lessonLevel}/student/{studentId}/course/{courseId}")
    suspend fun getLessonProgressWithCourse(
        @Path("lessonLevel") lessonLevel: String,
        @Path("studentId") studentId: Int,
        @Path("courseId") courseId: Int
    ): Response<LessonProgress>

    @POST("student-progress")
    suspend fun updateStudentProgress(@Body progress: Map<String, Any>): Response<Map<String, Any>>

    @GET("progress/course/{courseId}/student/{studentId}/detailed")
    suspend fun getDetailedCourseProgress(
        @Path("courseId") courseId: Int,
        @Path("studentId") studentId: Int
    ): Response<CourseProgress>

    @GET("progress/student/{studentId}/overview/detailed")
    suspend fun getStudentProgressOverview(@Path("studentId") studentId: Int): Response<OverallProgress>

    // ==================== TEACHER COURSE ASSIGNMENTS & QUESTIONS ====================
    @GET("teacher-assignments/course/{courseId}")
    suspend fun getTeacherAssignmentsByCourseId(@Path("courseId") courseId: Int): Response<List<Assignment>>

    @GET("teacher-assignments/{assignmentId}")
    suspend fun getTeacherAssignmentById(@Path("assignmentId") assignmentId: Int): Response<Assignment>

    @GET("teacher-assignments/teacher/{teacherId}")
    suspend fun getAssignmentsByTeacherId(@Path("teacherId") teacherId: Int): Response<TeacherAssignmentResponse>

    @POST("teacher-assignments")
    suspend fun createTeacherAssignment(@Body assignment: Map<String, Any>): Response<Map<String, Any>>

    @PUT("teacher-assignments/{assignmentId}")
    suspend fun updateTeacherAssignment(
        @Path("assignmentId") assignmentId: Int,
        @Body assignment: Map<String, Any>
    ): Response<Map<String, Any>>

    @DELETE("teacher-assignments/{assignmentId}")
    suspend fun deleteTeacherAssignment(@Path("assignmentId") assignmentId: Int): Response<Map<String, Any>>

    // ==================== QUESTIONS & ANSWERS ====================


    @GET("questions/{questionId}")
    suspend fun getQuestionById(@Path("questionId") questionId: Int): Response<Question>

    @GET("questions/lesson-part/{lessonPartId}")
    suspend fun getQuestionsByLessonPart(@Path("lessonPartId") lessonPartId: Int): Response<List<Question>>

    @GET("answers/question/{questionId}")
    suspend fun getAnswersForQuestion(@Path("questionId") questionId: Int): Response<List<Answer>>

    // Note: Use PUT student-answers/student/{studentId}/course/{courseId}/lesson-part/{lessonPartId} instead

    @POST("lesson-part-scores")
    suspend fun submitLessonPartScore(@Body scoreData: LessonPartScoreRequest): Response<LessonPartScoreResponse>

    @GET("student-answers/student/{studentId}/course/{courseId}/lesson-part/{lessonPartId}/answered-at/{answeredAt}")
    suspend fun getAnswersByStudentCourseAndLessonPartAndDate(
        @Path("studentId") studentId: Int,
        @Path("courseId") courseId: Int,
        @Path("lessonPartId") lessonPartId: Int,
        @Path("answeredAt") answeredAt: String
    ): Response<Map<String, Any>>

    @GET("student-answers/student/{studentId}/course/{courseId}/lesson-part/{lessonPartId}")
    suspend fun getStudentAnswersByCourseAndLessonPart(
        @Path("studentId") studentId: Int,
        @Path("courseId") courseId: Int,
        @Path("lessonPartId") lessonPartId: Int
    ): Response<Map<String, Any>>

    // ==================== NEW STUDENT ANSWER MANAGEMENT ====================
    @POST("student-answers/student/{studentId}/course/{courseId}/lesson-part/{lessonPartId}")
    suspend fun submitStudentAnswers(
        @Path("studentId") studentId: Int,
        @Path("courseId") courseId: Int,
        @Path("lessonPartId") lessonPartId: Int,
        @Body request: StudentAnswersUpdateRequest
    ): Response<StudentAnswersUpdateResponse>

    @GET("student-answers/recent-submission/student/{studentId}/course/{courseId}/lesson-part/{lessonPartId}")
    suspend fun getRecentSubmissionScoreAndProgress(
        @Path("studentId") studentId: Int,
        @Path("courseId") courseId: Int,
        @Path("lessonPartId") lessonPartId: Int,
        @Query("submission_time") submissionTime: String? = null
    ): Response<RecentSubmissionResponse>

    // ==================== CLASS POSTS & COMMUNICATION ====================
    // Class Posts - Get posts by course
    @GET("class-posts/course/{courseId}")
    suspend fun getClassPostsByCourseId(@Path("courseId") courseId: Int): Response<List<ClassPost>>

    // Class Posts - Get posts with comments (recommended for ClassStreamScreen)
    @GET("class-posts/course/{courseId}/posts-with-comments")
    suspend fun getPostsWithCommentsByCourse(@Path("courseId") courseId: Int): Response<List<ClassPost>>

    // Class Posts - Get single post by ID
    @GET("class-posts/{postId}")
    suspend fun getClassPostById(@Path("postId") postId: Int): Response<ClassPost>

    @POST("class-posts")
    suspend fun createClassPost(@Body request: CreateClassPostRequest): Response<ClassPostResponse>

    @PUT("class-posts/{postId}")
    suspend fun updateClassPost(
        @Path("postId") postId: Int,
        @Body request: UpdateClassPostRequest
    ): Response<ClassPostResponse>

    @DELETE("class-posts/{postId}")
    suspend fun deleteClassPost(@Path("postId") postId: Int): Response<DeleteResponse>

    // Class Post Comments - Get comments by post ID
    @GET("class-post-replies/post/{postId}")
    suspend fun getClassPostReplies(@Path("postId") postId: Int): Response<List<ClassPostComment>>

    @POST("class-post-replies")
    suspend fun createClassPostReply(@Body request: CreateClassPostCommentRequest): Response<ClassPostCommentResponse>

    @PUT("class-post-replies/{commentId}")
    suspend fun updateClassPostReply(
        @Path("commentId") commentId: Int,
        @Body request: UpdateClassPostCommentRequest
    ): Response<ClassPostCommentResponse>

    @DELETE("class-post-replies/{commentId}")
    suspend fun deleteClassPostReply(@Path("commentId") commentId: Int): Response<DeleteResponse>

    // Additional endpoints for better data management

    // Get posts by teacher (for teacher's own posts)
    @GET("class-posts/teacher/{teacherId}")
    suspend fun getClassPostsByTeacher(@Path("teacherId") teacherId: Int): Response<List<ClassPost>>

    // Get comments by user (teacher or student)
    @GET("class-post-replies/teacher/{teacherId}")
    suspend fun getCommentsByTeacher(@Path("teacherId") teacherId: Int): Response<List<ClassPostComment>>

    @GET("class-post-replies/student/{studentId}")
    suspend fun getCommentsByStudent(@Path("studentId") studentId: Int): Response<List<ClassPostComment>>

    // ==================== NOTIFICATIONS ====================
    @GET("notifications/student/{studentId}")
    suspend fun getNotificationsByStudentId(@Path("studentId") studentId: Int): Response<List<Notification>>

    @GET("notifications/{notificationId}")
    suspend fun getNotificationById(@Path("notificationId") notificationId: Int): Response<Notification>

    @POST("notifications")
    suspend fun createNotification(@Body request: CreateNotificationRequest): Response<NotificationResponse>

    @PUT("notifications/{notificationId}")
    suspend fun updateNotification(
        @Path("notificationId") notificationId: Int,
        @Body request: UpdateNotificationRequest
    ): Response<NotificationResponse>

    @DELETE("notifications/{notificationId}")
    suspend fun deleteNotification(@Path("notificationId") notificationId: Int): Response<Map<String, Any>>

    // ==================== EXAM RESULTS & EVALUATION ====================
    @GET("exam-results/{examId}")
    suspend fun getExamById(@Path("examId") examId: Int): Response<ExamResult>

    @GET("exam-results/student/{studentId}")
    suspend fun getExamResultsByStudentId(@Path("studentId") studentId: Int): Response<List<ExamResult>>

    @GET("exam-results/course/{courseId}/student/{studentId}")
    suspend fun getExamResultsByCourseAndStudent(
        @Path("courseId") courseId: Int,
        @Path("studentId") studentId: Int
    ): Response<List<ExamResult>>

    @POST("exam-results")
    suspend fun submitExamResult(@Body examResult: Map<String, Any>): Response<Map<String, Any>>

    @GET("evaluations/student/{studentId}")
    suspend fun getStudentEvaluations(@Path("studentId") studentId: Int): Response<List<StudentEvaluation>>

    @POST("evaluations")
    suspend fun createStudentEvaluation(@Body evaluation: Map<String, Any>): Response<Map<String, Any>>

    // ==================== ALL ENDPOINTS COMPLETE ====================

    // ==================== STATISTICS & ANALYTICS ====================
    @GET("statistics/overview")
    suspend fun getOverviewStatistics(): Response<Map<String, Any>>

    @GET("statistics/courses")
    suspend fun getCourseStatistics(): Response<Map<String, Any>>

    @GET("statistics/students/performance")
    suspend fun getStudentPerformanceStatistics(): Response<Map<String, Any>>
}
