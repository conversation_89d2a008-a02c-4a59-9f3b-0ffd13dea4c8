package com.example.ckc_englihoo.API

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.ckc_englihoo.DataClass.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class AppViewModel : ViewModel() {
    private val apiService = RetrofitClient.apiService

    // ==================== AUTHENTICATION STATE ====================
    private val _currentStudent = MutableStateFlow<Student?>(null)
    val currentStudent: StateFlow<Student?> = _currentStudent.asStateFlow()

    private val _currentTeacher = MutableStateFlow<Teacher?>(null)
    val currentTeacher: StateFlow<Teacher?> = _currentTeacher.asStateFlow()

    private val _isLoggedIn = MutableStateFlow(false)
    val isLoggedIn: StateFlow<Boolean> = _isLoggedIn.asStateFlow()

    // ==================== DATA STATE ====================
    private val _students = MutableStateFlow<List<Student>>(emptyList())
    val students: StateFlow<List<Student>> = _students.asStateFlow()

    private val _teachers = MutableStateFlow<List<Teacher>>(emptyList())
    val teachers: StateFlow<List<Teacher>> = _teachers.asStateFlow()

    private val _courses = MutableStateFlow<List<Course>>(emptyList())
    val courses: StateFlow<List<Course>> = _courses.asStateFlow()

    private val _lessons = MutableStateFlow<List<Lesson>>(emptyList())
    val lessons: StateFlow<List<Lesson>> = _lessons.asStateFlow()

    private val _lessonParts = MutableStateFlow<List<LessonPart>>(emptyList())
    val lessonParts: StateFlow<List<LessonPart>> = _lessonParts.asStateFlow()

    private val _questions = MutableStateFlow<List<Question>>(emptyList())
    val questions: StateFlow<List<Question>> = _questions.asStateFlow()

    private val _answers = MutableStateFlow<List<Answer>>(emptyList())
    val answers: StateFlow<List<Answer>> = _answers.asStateFlow()

    private val _enrollments = MutableStateFlow<List<CourseEnrollment>>(emptyList())
    val enrollments: StateFlow<List<CourseEnrollment>> = _enrollments.asStateFlow()

    private val _classPosts = MutableStateFlow<List<ClassPost>>(emptyList())
    val classPosts: StateFlow<List<ClassPost>> = _classPosts.asStateFlow()

    private val _currentCourseId = MutableStateFlow<Int?>(null)
    val currentCourseId: StateFlow<Int?> = _currentCourseId.asStateFlow()

    private val _classPostComments = MutableStateFlow<List<ClassPostComment>>(emptyList())
    val classPostComments: StateFlow<List<ClassPostComment>> = _classPostComments.asStateFlow()

    private val _notifications = MutableStateFlow<List<Notification>>(emptyList())
    val notifications: StateFlow<List<Notification>> = _notifications.asStateFlow()

    private val _examResults = MutableStateFlow<List<ExamResult>>(emptyList())
    val examResults: StateFlow<List<ExamResult>> = _examResults.asStateFlow()

    // ==================== ADDITIONAL STATE FOR UI ====================
    private val _courseEnrollments = MutableStateFlow<List<CourseEnrollment>>(emptyList())
    val courseEnrollments: StateFlow<List<CourseEnrollment>> = _courseEnrollments.asStateFlow()

    private val _teacherCourses = MutableStateFlow<List<Course>>(emptyList())
    val teacherCourses: StateFlow<List<Course>> = _teacherCourses.asStateFlow()

    private val _teacherCourseStudents = MutableStateFlow<Map<Int, List<Student>>>(emptyMap())
    val teacherCourseStudents: StateFlow<Map<Int, List<Student>>> = _teacherCourseStudents.asStateFlow()

    private val _courseStudentCounts = MutableStateFlow<Map<Int, Int>>(emptyMap())
    val courseStudentCounts: StateFlow<Map<Int, Int>> = _courseStudentCounts.asStateFlow()

    private val _lessonPartsWithProgress = MutableStateFlow<List<LessonPart>>(emptyList())
    val lessonPartsWithProgress: StateFlow<List<LessonPart>> = _lessonPartsWithProgress.asStateFlow()

    // ==================== PROGRESS STATE ====================
    private val _overallProgress = MutableStateFlow<OverallProgress?>(null)
    val overallProgress: StateFlow<OverallProgress?> = _overallProgress.asStateFlow()

    private val _courseProgress = MutableStateFlow<CourseProgress?>(null)
    val courseProgress: StateFlow<CourseProgress?> = _courseProgress.asStateFlow()

    // Map để lưu trữ progress của nhiều khóa học
    private val _courseProgressMap = MutableStateFlow<Map<Int, CourseProgress>>(emptyMap())
    val courseProgressMap: StateFlow<Map<Int, CourseProgress>> = _courseProgressMap.asStateFlow()

    private val _lessonPartProgress = MutableStateFlow<LessonPartProgress?>(null)
    val lessonPartProgress: StateFlow<LessonPartProgress?> = _lessonPartProgress.asStateFlow()

    private val _lessonPartScores = MutableStateFlow<List<LessonPartScore>>(emptyList())
    val lessonPartScores: StateFlow<List<LessonPartScore>> = _lessonPartScores.asStateFlow()

    // ==================== TEMPORARY STUDENT ANSWERS STORAGE ====================
    private val _tempStudentAnswers = MutableStateFlow<Map<Int, StudentAnswerItem>>(emptyMap())
    val tempStudentAnswers: StateFlow<Map<Int, StudentAnswerItem>> = _tempStudentAnswers.asStateFlow()

    // ==================== UI STATE ====================
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    // ==================== AUTHENTICATION METHODS ====================
    fun loginStudent(username: String, password: String) {
        viewModelScope.launch {
            _isLoading.value = true
            _errorMessage.value = null // Clear previous errors
            try {
                val response = apiService.kiemTraDangNhap(username, password)
                if (response.isSuccessful) {
                    response.body()?.let { studentList ->
                        if (studentList.isNotEmpty()) {
                            _currentStudent.value = studentList[0]
                            _isLoggedIn.value = true
                            Log.d("AppViewModel", "Student login successful: ${studentList[0].username}")
                        } else {
                            _errorMessage.value = "Tên đăng nhập hoặc mật khẩu không đúng"
                        }
                    } ?: run {
                        _errorMessage.value = "Tên đăng nhập hoặc mật khẩu không đúng"
                    }
                } else {
                    _errorMessage.value = when (response.code()) {
                        404 -> "Tài khoản học sinh không tồn tại"
                        401 -> "Tên đăng nhập hoặc mật khẩu không đúng"
                        403 -> "Tài khoản đã bị khóa"
                        500 -> "Lỗi máy chủ, vui lòng thử lại sau"
                        else -> "Đăng nhập thất bại (Mã lỗi: ${response.code()})"
                    }
                }
            } catch (e: Exception) {
                _errorMessage.value = when {
                    e.message?.contains("timeout", ignoreCase = true) == true ->
                        "Kết nối quá chậm, vui lòng thử lại"
                    e.message?.contains("network", ignoreCase = true) == true ->
                        "Không có kết nối mạng"
                    else -> "Lỗi kết nối: ${e.message}"
                }
                Log.e("AppViewModel", "Student login error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loginTeacher(username: String, password: String) {
        viewModelScope.launch {
            _isLoading.value = true
            _errorMessage.value = null // Clear previous errors
            try {
                val response = apiService.kiemTraDangNhapTeacher(username, password)
                if (response.isSuccessful) {
                    response.body()?.let { teacherList ->
                        if (teacherList.isNotEmpty()) {
                            _currentTeacher.value = teacherList[0]
                            _isLoggedIn.value = true
                            Log.d("AppViewModel", "Teacher login successful: ${teacherList[0].username}")
                        } else {
                            _errorMessage.value = "Tên đăng nhập hoặc mật khẩu không đúng"
                        }
                    } ?: run {
                        _errorMessage.value = "Tên đăng nhập hoặc mật khẩu không đúng"
                    }
                } else {
                    _errorMessage.value = when (response.code()) {
                        404 -> "Tài khoản giáo viên không tồn tại"
                        401 -> "Tên đăng nhập hoặc mật khẩu không đúng"
                        403 -> "Tài khoản đã bị khóa"
                        500 -> "Lỗi máy chủ, vui lòng thử lại sau"
                        else -> "Đăng nhập thất bại (Mã lỗi: ${response.code()})"
                    }
                }
            } catch (e: Exception) {
                _errorMessage.value = when {
                    e.message?.contains("timeout", ignoreCase = true) == true ->
                        "Kết nối quá chậm, vui lòng thử lại"
                    e.message?.contains("network", ignoreCase = true) == true ->
                        "Không có kết nối mạng"
                    else -> "Lỗi kết nối: ${e.message}"
                }
                Log.e("AppViewModel", "Teacher login error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun logout() {
        _currentStudent.value = null
        _currentTeacher.value = null
        _isLoggedIn.value = false
        clearAllData()
    }

    fun clearQuestions() {
        _questions.value = emptyList()
        Log.d("AppViewModel", "Cleared questions list.")
    }

    private fun clearAllData() {
        _students.value = emptyList()
        _teachers.value = emptyList()
        _courses.value = emptyList()
        _lessons.value = emptyList()
        _lessonParts.value = emptyList()
        _questions.value = emptyList()
        _answers.value = emptyList()
        _enrollments.value = emptyList()
        _classPosts.value = emptyList()
        _classPostComments.value = emptyList()
        _notifications.value = emptyList()
        _examResults.value = emptyList()
        _overallProgress.value = null
        _courseProgress.value = null
        _courseProgressMap.value = emptyMap()
        _lessonPartProgress.value = null
        _lessonPartScores.value = emptyList()
        _courseEnrollments.value = emptyList()
        _teacherCourses.value = emptyList()
        _teacherCourseStudents.value = emptyMap()
        _courseStudentCounts.value = emptyMap()
        _lessonPartsWithProgress.value = emptyList()
        _tempStudentAnswers.value = emptyMap()
    }

    // ==================== STUDENT METHODS ====================
    fun loadAllStudents() {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val response = apiService.getAllStudents()
                if (response.isSuccessful) {
                    response.body()?.let { studentList ->
                        _students.value = studentList
                        Log.d("AppViewModel", "Loaded ${studentList.size} students")
                    }
                } else {
                    _errorMessage.value = "Failed to load students: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error loading students", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadStudentById(studentId: Int) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val response = apiService.getStudentById(studentId)
                if (response.isSuccessful) {
                    response.body()?.let { student ->
                        _currentStudent.value = student
                        Log.d("AppViewModel", "Loaded student: ${student.username}")
                    }
                } else {
                    _errorMessage.value = "Failed to load student: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error loading student", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // ==================== COURSE METHODS ====================
    fun loadAllCourses() {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val response = apiService.getAllCourses()
                if (response.isSuccessful) {
                    response.body()?.let { courseList ->
                        _courses.value = courseList
                        Log.d("AppViewModel", "Loaded ${courseList.size} courses")
                    }
                } else {
                    _errorMessage.value = "Failed to load courses: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error loading courses", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadCoursesByStudentId(studentId: Int) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val response = apiService.getCoursesByStudentId(studentId)
                if (response.isSuccessful) {
                    response.body()?.let { courseList ->
                        _courses.value = courseList
                        Log.d("AppViewModel", "Loaded ${courseList.size} courses for student $studentId")
                    }
                } else {
                    _errorMessage.value = "Failed to load student courses: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error loading student courses", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadCoursesByLevel(level: String) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val response = apiService.getCoursesByLevel(level)
                if (response.isSuccessful) {
                    response.body()?.let { courseList ->
                        _courses.value = courseList
                        Log.d("AppViewModel", "Loaded ${courseList.size} courses for level $level")
                    }
                } else {
                    _errorMessage.value = "Failed to load courses by level: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error loading courses by level", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // ==================== ENROLLMENT METHODS ====================
    fun loadStudentEnrollments(studentId: Int) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val response = apiService.getStudentEnrollments(studentId)
                if (response.isSuccessful) {
                    response.body()?.let { enrollmentList ->
                        _enrollments.value = enrollmentList
                        _courseEnrollments.value = enrollmentList // Update both for compatibility
                        Log.d("AppViewModel", "Loaded ${enrollmentList.size} enrollments for student $studentId")
                    }
                } else {
                    _errorMessage.value = "Failed to load enrollments: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error loading enrollments", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun smartCourseRegistration(studentId: Int, level: String, schedule: String) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val request = mapOf(
                    "level" to level,
                    "schedule" to schedule
                )
                val response = apiService.smartCourseRegistration(studentId, request)
                if (response.isSuccessful) {
                    response.body()?.let { result ->
                        Log.d("AppViewModel", "Smart registration successful: $result")
                        // Reload enrollments after successful registration
                        loadStudentEnrollments(studentId)
                    }
                } else {
                    _errorMessage.value = "Registration failed: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error in smart registration", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // ==================== LESSON METHODS ====================
    fun loadLessonsByCourseId(courseId: Int) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val response = apiService.getLessonsByCourseId(courseId)
                if (response.isSuccessful) {
                    response.body()?.let { lessonList ->
                        _lessons.value = lessonList
                        Log.d("AppViewModel", "Loaded ${lessonList.size} lessons for course $courseId")
                    }
                } else {
                    _errorMessage.value = "Failed to load lessons: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error loading lessons", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadLessonsByLevel(level: String) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val response = apiService.getLessonByLevel(level)
                if (response.isSuccessful) {
                    response.body()?.let { lesson ->
                        _lessons.value = listOf(lesson)
                        Log.d("AppViewModel", "Loaded lesson for level $level: ${lesson.title}")
                    }
                } else {
                    _errorMessage.value = "Failed to load lessons by level: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error loading lessons by level", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // ==================== LESSON PART METHODS ====================
    fun loadLessonPartsByLevel(level: String) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val response = apiService.getLessonPartsByLevel(level)
                if (response.isSuccessful) {
                    response.body()?.let { lessonPartList: List<LessonPart> ->
                        _lessonParts.value = lessonPartList
                        Log.d("AppViewModel", "Loaded ${lessonPartList.size} lesson parts for level $level")
                    }
                } else {
                    _errorMessage.value = "Failed to load lesson parts: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error loading lesson parts", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadLessonPartsByCourse(courseId: Int) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val response = apiService.getLessonPartsByCourse(courseId)
                if (response.isSuccessful) {
                    response.body()?.let { lessonPartList ->
                        _lessonParts.value = lessonPartList
                        Log.d("AppViewModel", "Loaded ${lessonPartList.size} lesson parts for course $courseId")
                    }
                } else {
                    _errorMessage.value = "Failed to load lesson parts by course: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error loading lesson parts by course", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // ==================== QUESTION METHODS ====================
    fun loadQuestionsByLessonPart(lessonPartId: Int) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val response = apiService.getQuestionsByLessonPart(lessonPartId)
                if (response.isSuccessful) {
                    response.body()?.let { questionList ->
                        _questions.value = questionList
                        Log.d("AppViewModel", "Loaded ${questionList.size} questions for lesson part $lessonPartId")
                    }
                } else {
                    _errorMessage.value = "Failed to load questions: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error loading questions", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadAnswersForQuestion(questionId: Int) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val response = apiService.getAnswersForQuestion(questionId)
                if (response.isSuccessful) {
                    response.body()?.let { answerList ->
                        _answers.value = answerList
                        Log.d("AppViewModel", "Loaded ${answerList.size} answers for question $questionId")
                    }
                } else {
                    _errorMessage.value = "Failed to load answers: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error loading answers", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // ==================== PROGRESS METHODS ====================
    fun loadStudentOverallProgress(studentId: Int) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val response = apiService.getStudentOverallProgress(studentId)
                if (response.isSuccessful) {
                    response.body()?.let { progress ->
                        _overallProgress.value = progress
                        Log.d("AppViewModel", "Loaded overall progress for student $studentId")
                    }
                } else {
                    _errorMessage.value = "Failed to load overall progress: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error loading overall progress", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadCourseProgress(courseId: Int, studentId: Int) {
        viewModelScope.launch {
            try {
                val response = apiService.getCourseProgress(courseId, studentId)
                if (response.isSuccessful) {
                    response.body()?.let { progress ->
                        // Cập nhật single course progress
                        _courseProgress.value = progress

                        // Cập nhật course progress map
                        val currentMap = _courseProgressMap.value.toMutableMap()
                        currentMap[courseId] = progress
                        _courseProgressMap.value = currentMap

                        Log.d("AppViewModel", "Loaded course progress for student $studentId, course $courseId: ${progress.overall_progress_percentage}%")
                    }
                } else {
                    Log.e("AppViewModel", "Failed to load course progress: ${response.code()}")
                }
            } catch (e: Exception) {
                Log.e("AppViewModel", "Error loading course progress for course $courseId", e)
            }
        }
    }

    /**
     * Load course progress cho nhiều khóa học cùng lúc
     */
    fun loadMultipleCourseProgress(courseIds: List<Int>, studentId: Int) {
        viewModelScope.launch {
            Log.d("AppViewModel", "Loading progress for ${courseIds.size} courses")

            // Load progress cho từng khóa học song song
            courseIds.forEach { courseId ->
                launch {
                    try {
                        val response = apiService.getCourseProgress(courseId, studentId)
                        if (response.isSuccessful) {
                            response.body()?.let { progress ->
                                // Cập nhật course progress map thread-safe
                                synchronized(_courseProgressMap) {
                                    val currentMap = _courseProgressMap.value.toMutableMap()
                                    currentMap[courseId] = progress
                                    _courseProgressMap.value = currentMap
                                }

                                Log.d("AppViewModel", "Loaded course progress for course $courseId: ${progress.overall_progress_percentage}%")
                            }
                        } else {
                            Log.e("AppViewModel", "Failed to load course progress for course $courseId: ${response.code()}")
                        }
                    } catch (e: Exception) {
                        Log.e("AppViewModel", "Error loading course progress for course $courseId", e)
                    }
                }
            }
        }
    }

    fun loadLessonPartProgress(lessonPartId: Int, studentId: Int) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val response = apiService.getLessonPartProgress(lessonPartId, studentId)
                if (response.isSuccessful) {
                    response.body()?.let { progress ->
                        _lessonPartProgress.value = progress
                        Log.d("AppViewModel", "Loaded lesson part progress for student $studentId, lesson part $lessonPartId")
                    }
                } else {
                    _errorMessage.value = "Failed to load lesson part progress: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error loading lesson part progress", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadScoresByStudentId(studentId: Int) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val response = apiService.getScoresByStudentId(studentId)
                if (response.isSuccessful) {
                    response.body()?.let { scoreList ->
                        _lessonPartScores.value = scoreList
                        Log.d("AppViewModel", "Loaded ${scoreList.size} scores for student $studentId")
                    }
                } else {
                    _errorMessage.value = "Failed to load scores: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error loading scores", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // ==================== CLASS POST METHODS ====================
    fun loadClassPostsByCourseId(courseId: Int) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                // Use posts-with-comments endpoint for better performance
                val response = apiService.getPostsWithCommentsByCourse(courseId)
                if (response.isSuccessful) {
                    response.body()?.let { postList ->
                        _classPosts.value = postList
                        Log.d("AppViewModel", "Loaded ${postList.size} class posts with comments for course $courseId")
                    }
                } else {
                    _errorMessage.value = "Failed to load class posts: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error loading class posts", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadClassPostReplies(postId: Int) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val response = apiService.getClassPostReplies(postId)
                if (response.isSuccessful) {
                    response.body()?.let { commentList ->
                        _classPostComments.value = commentList
                        Log.d("AppViewModel", "Loaded ${commentList.size} comments for post $postId")
                    }
                } else {
                    _errorMessage.value = "Failed to load comments: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error loading comments", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // ==================== NOTIFICATION METHODS ====================
    fun loadNotificationsByStudentId(studentId: Int) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val response = apiService.getNotificationsByStudentId(studentId)
                if (response.isSuccessful) {
                    response.body()?.let { notificationList ->
                        _notifications.value = notificationList
                        Log.d("AppViewModel", "Loaded ${notificationList.size} notifications for student $studentId")
                    }
                } else {
                    _errorMessage.value = "Failed to load notifications: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error loading notifications", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // ==================== EXAM RESULT METHODS ====================
    fun loadExamResultsByStudentId(studentId: Int) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val response = apiService.getExamResultsByStudentId(studentId)
                if (response.isSuccessful) {
                    response.body()?.let { examResultList ->
                        _examResults.value = examResultList
                        Log.d("AppViewModel", "Loaded ${examResultList.size} exam results for student $studentId")
                    }
                } else {
                    _errorMessage.value = "Failed to load exam results: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error loading exam results", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // ==================== TEACHER METHODS ====================
    fun loadAllTeachers() {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val response = apiService.getAllTeachers()
                if (response.isSuccessful) {
                    response.body()?.let { teacherList ->
                        _teachers.value = teacherList
                        Log.d("AppViewModel", "Loaded ${teacherList.size} teachers")
                    }
                } else {
                    _errorMessage.value = "Failed to load teachers: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error loading teachers", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // ==================== MISSING METHODS (PLACEHOLDER) ====================
    // These methods are referenced in UI but not implemented yet
    fun loadClassPostsByCourse(courseId: Int) {
        loadClassPostsByCourseId(courseId)
    }

    /**
     * Load posts with comments using the new API endpoint
     */
    fun loadPostsWithCommentsByCourse(courseId: Int) {
        viewModelScope.launch {
            _isLoading.value = true
            _currentCourseId.value = courseId
            try {
                Log.d("AppViewModel", "Loading posts with comments for course $courseId")
                val response = apiService.getPostsWithCommentsByCourse(courseId)
                if (response.isSuccessful) {
                    response.body()?.let { posts ->
                        _classPosts.value = posts
                        Log.d("AppViewModel", "Loaded ${posts.size} posts with comments for course $courseId")
                        posts.forEach { post ->
                            Log.d("AppViewModel", "Post ${post.post_id}: ${post.comments?.size ?: 0} comments")
                        }
                    }
                } else {
                    _errorMessage.value = "Failed to load posts with comments: ${response.code()}"
                    Log.e("AppViewModel", "Failed to load posts with comments: ${response.code()}")
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error loading posts with comments", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadStudentExamResults(studentId: Int) {
        loadExamResultsByStudentId(studentId)
    }

    fun loadExamResultsByCourseAndStudent(courseId: Int, studentId: Int) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val response = apiService.getExamResultsByCourseAndStudent(courseId, studentId)
                if (response.isSuccessful) {
                    response.body()?.let { apiResponse ->
                        if (apiResponse.success && apiResponse.data != null) {
                            _examResults.value = apiResponse.data
                            Log.d("AppViewModel", "Loaded ${apiResponse.data.size} exam results for course $courseId and student $studentId")
                        } else {
                            val errorMsg = apiResponse.message ?: "Unknown error"
                            _errorMessage.value = "Failed to load exam results: $errorMsg"
                            Log.e("AppViewModel", "API returned success=false or data=null: $errorMsg")
                        }
                    } ?: run {
                        _errorMessage.value = "Empty response body"
                        Log.e("AppViewModel", "Empty response body for exam results")
                    }
                } else {
                    _errorMessage.value = "Failed to load exam results: ${response.code()}"
                    Log.e("AppViewModel", "HTTP error ${response.code()}: ${response.message()}")
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error loading exam results", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun submitExamResult(
        examId: Int,
        studentId: Int,
        courseId: Int,
        examDate: String,
        listeningScore: Double,
        speakingScore: Double,
        readingScore: Double,
        writingScore: Double,
        overallStatus: Int,
        onSuccess: (ExamResult, Double) -> Unit = { _, _ -> },
        onError: (String) -> Unit = {}
    ) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val examData = mapOf(
                    "student_id" to studentId,
                    "course_id" to courseId,
                    "exam_date" to examDate,
                    "listening_score" to listeningScore,
                    "speaking_score" to speakingScore,
                    "reading_score" to readingScore,
                    "writing_score" to writingScore,
                    "overall_status" to overallStatus
                )

                val response = apiService.submitExamResult(examId, examData)
                if (response.isSuccessful) {
                    response.body()?.let { apiResponse ->
                        if (apiResponse.success && apiResponse.data != null) {
                            val examResult = apiResponse.data
                            // Use average_score from API response if available, otherwise calculate
                            val averageScore = try {
                                // Try to get average_score from response (if API provides it)
                                (apiResponse as? Map<String, Any>)?.get("average_score") as? Double
                                    ?: (listeningScore + speakingScore + readingScore + writingScore) / 4.0
                            } catch (e: Exception) {
                                (listeningScore + speakingScore + readingScore + writingScore) / 4.0
                            }

                            Log.d("AppViewModel", "Exam result submitted successfully: ${apiResponse.message ?: "Success"}")
                            Log.d("AppViewModel", "Average score: ${String.format("%.2f", averageScore)}")

                            // Reload exam results to get updated data
                            loadExamResultsByStudentId(studentId)

                            onSuccess(examResult, averageScore)
                        } else {
                            val errorMsg = "Failed to submit exam result: ${apiResponse.message ?: "Unknown error"}"
                            _errorMessage.value = errorMsg
                            onError(errorMsg)
                            Log.e("AppViewModel", "API returned success=false or data=null: ${apiResponse.message}")
                        }
                    } ?: run {
                        val errorMsg = "Empty response body"
                        _errorMessage.value = errorMsg
                        onError(errorMsg)
                        Log.e("AppViewModel", "Empty response body for exam result submission")
                    }
                } else {
                    val errorMsg = "Failed to submit exam result: ${response.code()}"
                    _errorMessage.value = errorMsg
                    onError(errorMsg)
                    Log.e("AppViewModel", "HTTP error ${response.code()}: ${response.message()}")
                }
            } catch (e: Exception) {
                val errorMsg = "Network error: ${e.message}"
                _errorMessage.value = errorMsg
                onError(errorMsg)
                Log.e("AppViewModel", "Error submitting exam result", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadOverallProgress(studentId: Int) {
        loadStudentOverallProgress(studentId)
    }

    fun loadStudentNotifications(studentId: Int) {
        loadNotificationsByStudentId(studentId)
    }

    fun refreshCurrentStudentData(studentId: Int) {
        loadStudentById(studentId)
        loadStudentOverallProgress(studentId)
        loadStudentEnrollments(studentId)
    }

    /**
     * Load teacher assignments and their associated courses
     */
    fun loadTeacherCourses(teacherId: Int) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                Log.d("AppViewModel", "Loading teacher assignments for teacher $teacherId")

                // Load teacher assignments
                val assignmentsResponse = apiService.getAssignmentsByTeacherId(teacherId)
                if (assignmentsResponse.isSuccessful) {
                    assignmentsResponse.body()?.let { response ->
                        if (response.success) {
                            Log.d("AppViewModel", "Loaded ${response.data.size} assignments")

                            // Extract course IDs from assignments
                            val courseIds = response.data.map { it.course.courseId }

                            // Load detailed course information for each course
                            val detailedCourses = mutableListOf<Course>()
                            val courseStudentsMap = mutableMapOf<Int, List<Student>>()

                            courseIds.forEach { courseId ->
                                try {
                                    val courseResponse = apiService.getCourseById(courseId)
                                    if (courseResponse.isSuccessful) {
                                        courseResponse.body()?.let { course ->
                                            detailedCourses.add(course)

                                            // Extract students from course details
                                            val courseStudents = course.students
                                            courseStudentsMap[courseId] = courseStudents

                                            Log.d("AppViewModel", "Loaded course details: ${course.course_name} with ${courseStudents.size} students")
                                        }
                                    }
                                } catch (e: Exception) {
                                    Log.e("AppViewModel", "Error loading course $courseId", e)
                                }
                            }

                            // Update teacher courses and students state
                            _teacherCourses.value = detailedCourses
                            _teacherCourseStudents.value = courseStudentsMap
                            Log.d("AppViewModel", "Updated teacher courses: ${detailedCourses.size} courses with students")
                        }
                    }
                } else {
                    _errorMessage.value = "Failed to load teacher assignments: ${assignmentsResponse.code()}"
                    Log.e("AppViewModel", "Failed to load teacher assignments: ${assignmentsResponse.code()}")
                }
            } catch (e: Exception) {
                _errorMessage.value = "Error loading teacher courses: ${e.message}"
                Log.e("AppViewModel", "Error loading teacher courses", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadMultipleCourseStudentCounts(courseIds: List<Int>) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val counts = mutableMapOf<Int, Int>()
                for (courseId in courseIds) {
                    val response = apiService.getCourseStudentCount(courseId)
                    if (response.isSuccessful) {
                        response.body()?.let { result ->
                            // Assuming the response has a "total_students" field
                            val count = (result["total_students"] as? Number)?.toInt() ?: 0
                            counts[courseId] = count
                        }
                    }
                }
                _courseStudentCounts.value = counts
                Log.d("AppViewModel", "Loaded student counts for ${counts.size} courses")
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error loading course student counts", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadStudentsByEnrollment(courseId: Int) {
        // This would need a specific API endpoint
        // For now, load all students
        loadAllStudents()
    }

    fun loadLessonsByCourse(courseId: Int) {
        loadLessonsByCourseId(courseId)
    }

    fun loadStudentScores(studentId: Int) {
        loadScoresByStudentId(studentId)
    }

    fun loadLessonPartsByCourseWithProgress(courseId: Int, studentId: Int) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val response = apiService.getLessonPartsByCourse(courseId)
                if (response.isSuccessful) {
                    response.body()?.let { lessonPartList ->
                        _lessonParts.value = lessonPartList
                        _lessonPartsWithProgress.value = lessonPartList
                        Log.d("AppViewModel", "Loaded ${lessonPartList.size} lesson parts with progress for course $courseId")
                    }
                } else {
                    _errorMessage.value = "Failed to load lesson parts: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error loading lesson parts with progress", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadLessonPartScoresByCourse(courseId: Int) {
        // This would need a specific API endpoint
        // For now, placeholder
        Log.d("AppViewModel", "loadLessonPartScoresByCourse not implemented")
    }

    fun enrollStudentInCourse(studentId: Int, courseId: Int) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val enrollment = mapOf(
                    "student_id" to studentId,
                    "course_id" to courseId
                )
                val response = apiService.enrollStudent(enrollment)
                if (response.isSuccessful) {
                    Log.d("AppViewModel", "Student enrolled successfully")
                    loadStudentEnrollments(studentId)
                } else {
                    _errorMessage.value = "Enrollment failed: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error enrolling student", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun createClassPost(courseId: Int, title: String, content: String, teacherId: Int) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val request = CreateClassPostRequest(
                    course_id = courseId,
                    teacher_id = teacherId,
                    title = title,
                    content = content
                )
                val response = apiService.createClassPost(request)
                if (response.isSuccessful) {
                    response.body()?.let { apiResponse ->
                        if (apiResponse.success) {
                            Log.d("AppViewModel", "Class post created successfully: ${apiResponse.message}")
                            loadPostsWithCommentsByCourse(courseId)
                        } else {
                            _errorMessage.value = "Failed to create post: ${apiResponse.message}"
                            Log.e("AppViewModel", "Failed to create post: ${apiResponse.message}")
                        }
                    }
                } else {
                    _errorMessage.value = "Failed to create post: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error creating post", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun createClassPostComment(postId: Int, content: String, studentId: Int? = null, teacherId: Int? = null) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val request = CreateClassPostCommentRequest(
                    post_id = postId,
                    student_id = studentId,
                    teacher_id = teacherId,
                    content = content
                )
                val response = apiService.createClassPostReply(request)
                if (response.isSuccessful) {
                    response.body()?.let { apiResponse ->
                        if (apiResponse.success) {
                            Log.d("AppViewModel", "Comment created successfully: ${apiResponse.message}")
                            // Reload posts to get updated comments
                            _currentCourseId.value?.let { courseId ->
                                loadPostsWithCommentsByCourse(courseId)
                            }
                        } else {
                            _errorMessage.value = "Failed to create comment: ${apiResponse.message}"
                            Log.e("AppViewModel", "Failed to create comment: ${apiResponse.message}")
                        }
                    }
                } else {
                    _errorMessage.value = "Failed to create comment: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error creating comment", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun updateClassPost(postId: Int, title: String? = null, content: String? = null, status: Int? = null) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val request = UpdateClassPostRequest(
                    title = title,
                    content = content,
                    status = status
                )
                val response = apiService.updateClassPost(postId, request)
                if (response.isSuccessful) {
                    response.body()?.let { apiResponse ->
                        if (apiResponse.success) {
                            Log.d("AppViewModel", "Post updated successfully: ${apiResponse.message}")
                            // Reload posts to get updated data
                            loadPostsWithCommentsByCourse(apiResponse.data.course_id)
                        } else {
                            _errorMessage.value = "Failed to update post: ${apiResponse.message}"
                            Log.e("AppViewModel", "Failed to update post: ${apiResponse.message}")
                        }
                    }
                } else {
                    _errorMessage.value = "Failed to update post: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error updating post", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun deleteClassPost(postId: Int, courseId: Int) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val response = apiService.deleteClassPost(postId)
                if (response.isSuccessful) {
                    response.body()?.let { apiResponse ->
                        if (apiResponse.success) {
                            Log.d("AppViewModel", "Post deleted successfully: ${apiResponse.message}")
                            // Reload posts to get updated list
                            loadClassPostsByCourseId(courseId)
                        } else {
                            _errorMessage.value = "Failed to delete post: ${apiResponse.message}"
                            Log.e("AppViewModel", "Failed to delete post: ${apiResponse.message}")
                        }
                    }
                } else {
                    _errorMessage.value = "Failed to delete post: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error deleting post", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun updateClassPostComment(commentId: Int, content: String? = null, status: Int? = null) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val request = UpdateClassPostCommentRequest(
                    content = content,
                    status = status
                )
                val response = apiService.updateClassPostReply(commentId, request)
                if (response.isSuccessful) {
                    response.body()?.let { apiResponse ->
                        if (apiResponse.success) {
                            Log.d("AppViewModel", "Comment updated successfully: ${apiResponse.message}")
                            // Reload posts to get updated comments
                            _currentCourseId.value?.let { courseId ->
                                loadPostsWithCommentsByCourse(courseId)
                            }
                        } else {
                            _errorMessage.value = "Failed to update comment: ${apiResponse.message}"
                            Log.e("AppViewModel", "Failed to update comment: ${apiResponse.message}")
                        }
                    }
                } else {
                    _errorMessage.value = "Failed to update comment: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error updating comment", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun deleteClassPostComment(commentId: Int) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val response = apiService.deleteClassPostReply(commentId)
                if (response.isSuccessful) {
                    response.body()?.let { apiResponse ->
                        if (apiResponse.success) {
                            Log.d("AppViewModel", "Comment deleted successfully: ${apiResponse.message}")
                            // Reload current posts to refresh comments
                            _currentCourseId.value?.let { courseId ->
                                loadPostsWithCommentsByCourse(courseId)
                            }
                        } else {
                            _errorMessage.value = "Failed to delete comment: ${apiResponse.message}"
                            Log.e("AppViewModel", "Failed to delete comment: ${apiResponse.message}")
                        }
                    }
                } else {
                    _errorMessage.value = "Failed to delete comment: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error deleting comment", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // ==================== NOTIFICATION MANAGEMENT ====================
    fun createNotification(adminId: Int, title: String, message: String, notificationDate: String) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val request = CreateNotificationRequest(
                    admin_id = adminId,
                    title = title,
                    message = message,
                    notification_date = notificationDate
                )
                val response = apiService.createNotification(request)
                if (response.isSuccessful) {
                    response.body()?.let { notificationResponse ->
                        if (notificationResponse.success) {
                            Log.d("AppViewModel", "Notification created successfully")
                            // Refresh notifications list if needed
                        } else {
                            _errorMessage.value = notificationResponse.message
                        }
                    }
                } else {
                    _errorMessage.value = "Failed to create notification: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error creating notification", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun updateNotification(notificationId: Int, title: String? = null, message: String? = null, notificationDate: String? = null) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val request = UpdateNotificationRequest(
                    title = title,
                    message = message,
                    notification_date = notificationDate
                )
                val response = apiService.updateNotification(notificationId, request)
                if (response.isSuccessful) {
                    response.body()?.let { notificationResponse ->
                        if (notificationResponse.success) {
                            Log.d("AppViewModel", "Notification updated successfully")
                            // Update local notification list
                            notificationResponse.notification?.let { updatedNotification ->
                                val currentNotifications = _notifications.value.toMutableList()
                                val index = currentNotifications.indexOfFirst { it.notification_id == notificationId }
                                if (index != -1) {
                                    currentNotifications[index] = updatedNotification
                                    _notifications.value = currentNotifications
                                }
                            }
                        } else {
                            _errorMessage.value = notificationResponse.message
                        }
                    }
                } else {
                    _errorMessage.value = "Failed to update notification: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error updating notification", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun deleteNotification(notificationId: Int) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val response = apiService.deleteNotification(notificationId)
                if (response.isSuccessful) {
                    Log.d("AppViewModel", "Notification deleted successfully")
                    // Remove from local list
                    val currentNotifications = _notifications.value.toMutableList()
                    currentNotifications.removeAll { it.notification_id == notificationId }
                    _notifications.value = currentNotifications
                } else {
                    _errorMessage.value = "Failed to delete notification: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Network error: ${e.message}"
                Log.e("AppViewModel", "Error deleting notification", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // ==================== STUDENT ANSWER SUBMISSION ====================
    fun submitStudentAnswers(
        studentId: Int,
        courseId: Int,
        lessonPartId: Int,
        answers: List<StudentAnswerItem>,
        onSuccess: (StudentAnswersUpdateResponse) -> Unit = {},
        onError: (String) -> Unit = {}
    ) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val request = StudentAnswersUpdateRequest(answers)
                val response = apiService.submitStudentAnswers(
                    studentId, courseId, lessonPartId, request
                )
                if (response.isSuccessful) {
                    response.body()?.let { result ->
                        onSuccess(result)
                    } ?: run {
                        val msg = "Empty response body"
                        _errorMessage.value = msg
                        onError(msg)
                    }
                } else {
                    val msg = "Submission failed: ${response.code()} ${response.message()}"
                    _errorMessage.value = msg
                    onError(msg)
                }
            } catch (e: Exception) {
                val msg = "Network error: ${e.localizedMessage}"
                _errorMessage.value = msg
                onError(msg)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun submitLessonPartScore(
        studentId: Int,
        lessonPartId: Int,
        courseId: Int,
        score: Double,
        totalQuestions: Int,
        correctAnswers: Int,
        onSuccess: (LessonPartScoreResponse) -> Unit = {},
        onError: (String) -> Unit = {}
    ) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val scoreRequest = LessonPartScoreRequest(
                    student_id = studentId,
                    lesson_part_id = lessonPartId,
                    course_id = courseId,
                    attempt_no = null, // Auto-calculated by server
                    score = score,
                    total_questions = totalQuestions,
                    correct_answers = correctAnswers
                )

                val response = apiService.submitLessonPartScore(scoreRequest)
                if (response.isSuccessful) {
                    response.body()?.let { result ->
                        Log.d("AppViewModel", "Lesson part score submitted successfully")
                        onSuccess(result)
                    }
                } else {
                    val errorMsg = "Failed to submit score: ${response.code()}"
                    _errorMessage.value = errorMsg
                    onError(errorMsg)
                }
            } catch (e: Exception) {
                val errorMsg = "Network error: ${e.message}"
                _errorMessage.value = errorMsg
                onError(errorMsg)
                Log.e("AppViewModel", "Error submitting score", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun getRecentSubmissionScore(
        studentId: Int,
        courseId: Int,
        lessonPartId: Int,
        submissionTime: String,
        onSuccess: (Map<String, Any>) -> Unit = {},
        onError: (String) -> Unit = {}
    ) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val response = apiService.getRecentSubmissionScoreAndProgress(
                    studentId, courseId, lessonPartId, submissionTime
                )
                if (response.isSuccessful) {
                    response.body()?.let { result ->
                        Log.d("AppViewModel", "Recent submission data retrieved")
                    }
                } else {
                    val errorMsg = "Failed to get submission score: ${response.code()}"
                    _errorMessage.value = errorMsg
                    onError(errorMsg)
                }
            } catch (e: Exception) {
                val errorMsg = "Network error: ${e.message}"
                _errorMessage.value = errorMsg
                onError(errorMsg)
                Log.e("AppViewModel", "Error getting submission score", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // Clear error message
    fun clearErrorMessage() {
        _errorMessage.value = null
    }

    // ==================== TEMPORARY STUDENT ANSWERS MANAGEMENT ====================

    /**
     * Save a single student answer temporarily during exercise completion
     * Now with automatic evaluation using QuestionHandler
     */
    fun saveStudentAnswer(questionId: Int, answerText: String, isCorrect: Boolean? = null) {
        val currentAnswers = _tempStudentAnswers.value.toMutableMap()

        // Tìm question để đánh giá đáp án
        val question = _questions.value.find { it.questions_id == questionId }
        val evaluatedIsCorrect = if (question != null && isCorrect == null) {
            // Sử dụng QuestionHandler để đánh giá
            val result = com.example.ckc_englihoo.Utils.QuestionHandler.evaluateAnswer(question, answerText)
            result.isCorrect
        } else {
            isCorrect
        }

        // Chuẩn hóa đáp án nếu có question
        val normalizedAnswer = if (question != null) {
            com.example.ckc_englihoo.Utils.QuestionHandler.normalizeAnswer(question, answerText)
        } else {
            answerText
        }

        currentAnswers[questionId] = StudentAnswerItem(
            question_id = questionId,
            answer_text = normalizedAnswer,
            is_correct = evaluatedIsCorrect
        )
        _tempStudentAnswers.value = currentAnswers
        Log.d("AppViewModel", "Saved temporary answer for question $questionId: $normalizedAnswer (isCorrect: $evaluatedIsCorrect)")
    }



    /**
     * Save multiple student answers at once with automatic evaluation
     */
    fun saveStudentAnswers(answers: Map<Int, String>) {
        val newAnswers = _tempStudentAnswers.value.toMutableMap()
        val questions = _questions.value

        answers.forEach { (questionId, answerText) ->
            val question = questions.find { it.questions_id == questionId }

            val (normalizedAnswer, isCorrect) = if (question != null) {
                val result = com.example.ckc_englihoo.Utils.QuestionHandler.evaluateAnswer(question, answerText)
                result.normalizedAnswer to result.isCorrect
            } else {
                answerText to null
            }

            newAnswers[questionId] = StudentAnswerItem(
                question_id = questionId,
                answer_text = normalizedAnswer,
                is_correct = isCorrect
            )
        }
        _tempStudentAnswers.value = newAnswers
        Log.d("AppViewModel", "Saved multiple temporary answers with evaluation.")
    }

    /**
     * Get temporary answer for a specific question
     */
    fun getTempAnswerForQuestion(questionId: Int): StudentAnswerItem? {
        return _tempStudentAnswers.value[questionId]
    }

    /**
     * Get all temporary answers
     */
    fun getAllTempAnswers(): Map<Int, StudentAnswerItem> {
        return _tempStudentAnswers.value
    }

    /**
     * Clear all temporary answers (call after successful submission)
     */
    fun clearTempStudentAnswers() {
        _tempStudentAnswers.value = emptyMap()
        Log.d("AppViewModel", "Cleared all temporary student answers")
    }


    /**
     * Submit all temporary answers to the server with validation
     */
    fun submitAllTempAnswers(
        studentId: Int,
        courseId: Int,
        lessonPartId: Int,
        onSuccess: (StudentAnswersUpdateResponse) -> Unit = {},
        onError: (String) -> Unit = {}
    ) {
        val temp = _tempStudentAnswers.value.orEmpty()
        val questions = _questions.value

        if (temp.isEmpty()) {
            val msg = "Không có câu trả lời nào để nộp"
            onError(msg)
            return
        }

        // Validate tất cả đáp án trước khi submit
        val canSubmit = com.example.ckc_englihoo.Utils.AnswerValidator.canSubmit(questions, temp)
        if (!canSubmit) {
            val errors = com.example.ckc_englihoo.Utils.AnswerValidator.getValidationErrors(questions, temp)
            val msg = "Có lỗi trong đáp án:\n${errors.joinToString("\n")}"
            onError(msg)
            return
        }

        // Đánh giá tất cả đáp án bằng QuestionHandler
        val userAnswers = temp.mapValues { it.value.answer_text }
        val evaluationResults = com.example.ckc_englihoo.Utils.QuestionHandler.evaluateAllAnswers(questions, userAnswers)

        // Tạo danh sách StudentAnswerItem với kết quả đánh giá
        val answerList = evaluationResults.map { result ->
            StudentAnswerItem(
                question_id = result.questionId,
                answer_text = result.normalizedAnswer,
                is_correct = result.isCorrect
            )
        }

        Log.d("AppViewModel", "Submitting ${answerList.size} evaluated answers")

        submitStudentAnswers(
            studentId, courseId, lessonPartId,
            answerList,
            onSuccess = { response ->
                // Clear temp answers after successful submission
                _tempStudentAnswers.value = emptyMap()
                onSuccess(response)
            },
            onError = onError
        )
    }

    /**
     * Get recent submission score and progress
     */
    fun getRecentSubmissionScoreAndProgress(
        studentId: Int,
        courseId: Int,
        lessonPartId: Int,
        submissionTime: String? = null,
        onSuccess: (RecentSubmissionData) -> Unit = {}, // Thay đổi kiểu trả về
        onError: (String) -> Unit = {}
    ) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.getRecentSubmissionScoreAndProgress(
                    studentId = studentId,
                    courseId = courseId,
                    lessonPartId = lessonPartId,
                    submissionTime = submissionTime
                )

                if (response.isSuccessful) {
                    response.body()?.let { result ->
                        if (result.success && result.data != null) {
                            Log.d("AppViewModel", "Successfully loaded recent submission result")
                            Log.d("AppViewModel", "Score: ${result.data.score}")
                            Log.d("AppViewModel", "Total questions: ${result.data.total_questions}")
                            Log.d("AppViewModel", "Correct answers: ${result.data.correct_answers}")
                            Log.d("AppViewModel", "Student answers count: ${result.data.student_answers?.size}")
                            onSuccess(result.data) // Truyền data vào onSuccess
                        } else {
                            Log.e("AppViewModel", "API returned success=false or data=null")
                            onError("Không có dữ liệu kết quả")
                        }
                    } ?: run {
                        Log.e("AppViewModel", "Empty response body for recent submission")
                        onError("Không có dữ liệu kết quả")
                    }
                } else {
                    val errorMsg = "Lỗi tải kết quả: ${response.code()}"
                    Log.e("AppViewModel", errorMsg)
                    onError(errorMsg)
                }
            } catch (e: Exception) {
                val errorMsg = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", errorMsg, e)
                onError(errorMsg)
            } finally {
                _isLoading.value = false
            }
        }
    }
}
