package com.example.ckc_englihoo.DataClass

/**
 * Request data class for submitting lesson part score
 */
data class LessonPartScoreRequest(
    val student_id: Int,
    val lesson_part_id: Int,
    val course_id: Int,
    val attempt_no: Int? = null,
    val score: Double,
    val total_questions: Int,
    val correct_answers: Int
)

/**
 * Response data class for lesson part score submission
 */
data class LessonPartScoreResponse(
    val success: Boolean,
    val message: String,
    val score_data: ScoreData? = null,
    val progress_updated: Boolean = false,
    val is_completed: Boolean = false,
    val course_progress_percentage: Double = 0.0
)

/**
 * Score data from API response
 */
data class ScoreData(
    val score_id: Int,
    val score: Double,
    val attempt_no: Int,
    val submit_time: String,
    val completion_percentage: Double
)

/**
 * Lesson Part Score entity - matches database structure
 */
data class LessonPartScore(
    val score_id: Int,
    val student_id: Int,
    val lesson_part_id: Int,
    val course_id: Int,
    val attempt_no: Int,
    val score: Double,
    val total_questions: Int,
    val correct_answers: Int,
    val submit_time: String,
    val created_at: String,
    val updated_at: String
)
