package com.example.ckc_englihoo.DataClass

// Class Post data class - Match với database structure
data class ClassPost(
    val post_id: Int,
    val course_id: Int,
    val teacher_id: Int, // Only teachers can create posts
    val title: String = "",
    val content: String = "",
    val status: Int = 1, // 1=active, 0=inactive
    val answered_at: String? = null, // Datetime field from database
    val created_at: String = "",
    val updated_at: String = "",
    val teacher: Teacher? = null, // Teacher object relationship
    val course: Course? = null,
    val comments: List<ClassPostComment> = emptyList()
)

// Class Post Comment data class - Match với API response
data class ClassPostComment(
    val comment_id: Int,
    val post_id: Int,
    val student_id: Int? = null, // Null if comment by teacher
    val teacher_id: Int? = null, // Null if comment by student
    val author_id: Int = 0, // For backward compatibility
    val author_type: String = "", // "teacher" or "student"
    val author_name: String? = null, // Author's full name
    val content: String = "",
    val status: Int = 1,
    val created_at: String = "",
    val updated_at: String = "",
    val author: Any? = null, // Dynamic - Teacher or Student object
    val post: ClassPost? = null // Reference to parent post
)

// Author Type Enum
enum class AuthorType(val value: String, val displayName: String) {
    TEACHER("teacher", "Giáo viên"),
    STUDENT("student", "Học sinh")
}

// Post Status Enum
enum class PostStatus(val value: Int, val displayName: String) {
    INACTIVE(0, "Ẩn"),
    ACTIVE(1, "Hiển thị")
}
