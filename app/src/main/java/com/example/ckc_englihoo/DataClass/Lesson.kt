package com.example.ckc_englihoo.DataClass

// Lesson data class - Match với API response
data class Lesson(
    val level: String = "", // Acts as ID - A1, A2, A3, A2/6, B1
    val title: String = "",
    val description: String = "",
    val order_index: Int = 0,
    val created_at: String = "",
    val updated_at: String = "",
    val lessonParts: List<LessonPart> = emptyList(), // API returns as "lessonParts"
    val courses: List<Course> = emptyList() // Chỉ có trong single lesson API
)

// Lesson Part data class - Match với API response
data class LessonPart(
    val lesson_part_id: Int,
    val level: String = "",
    val part_type: String = "", // Vocabulary, Grammar, Listening, Speaking, Reading, Writing, Pronunciation, Practice Test
    val content: String = "",
    val order_index: Int = 0,
    val created_at: String = "",
    val updated_at: String = "",
    val questions: List<Question> = emptyList(), // API returns as array of Question objects
    // Note: LessonPartContent table has been removed from database
    val lesson: Lesson? = null, // Chỉ có trong single lesson part API
    // Additional fields for UI compatibility
    val partType: String = part_type, // Alias for part_type
    val type: String = part_type, // Alias for part_type
    val description: String = "", // Add missing description field
    val progress: Float = 0.0f // Add missing progress field
)

// Note: LessonPartContent table has been removed from database schema

// Lesson Part With Progress data class - For UI that needs progress info
data class LessonPartWithProgress(
    val lesson_part_id: Int,
    val level: String = "",
    val part_type: String = "",
    val partType: String = "",
    val type: String = "",
    val content: String = "",
    val description: String = "",
    val progress: Float = 0.0f,
    val order_index: Int = 0,
    val total_questions: Int = 0,
    val is_completed: Boolean = false,
    val progress_percentage: Double = 0.0,
    val best_score: Double? = null,
    val last_attempt_date: String? = null,
    val created_at: String = "",
    val updated_at: String = "",
    val lesson: Lesson? = null
)

// LessonPartScore moved to LessonPartScore.kt to avoid duplication
