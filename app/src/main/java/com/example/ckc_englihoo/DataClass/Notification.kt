package com.example.ckc_englihoo.DataClass

// Notification data class - Match với API response (updated structure)
data class Notification(
    val notification_id: Int,
    val admin_id: Int,
    val title: String = "",
    val message: String = "",
    val notification_date: String = "",
    val created_at: String = "",
    val updated_at: String = "",
    val admin: Admin? = null, // Admin info from API
    // Additional fields for UI compatibility
    val date: String = notification_date, // Alias for notification_date
    val isRead: Boolean = false // Add missing isRead field for UI
)

// Admin data class for notification response
data class Admin(
    val admin_id: Int,
    val fullname: String,
    val username: String,
    val email: String,
    val email_verified_at: String?,
    val created_at: String,
    val updated_at: String
)

// Request/Response data classes for notification API
data class CreateNotificationRequest(
    val admin_id: Int,
    val title: String,
    val message: String,
    val notification_date: String
)

data class UpdateNotificationRequest(
    val title: String? = null,
    val message: String? = null,
    val notification_date: String? = null
)

data class NotificationResponse(
    val success: Boolean,
    val data: Notification? = null,
    val notification: Notification? = null, // Some responses use 'notification' instead of 'data'
    val message: String,
    val error: String? = null,
    val errors: Map<String, List<String>>? = null
)
