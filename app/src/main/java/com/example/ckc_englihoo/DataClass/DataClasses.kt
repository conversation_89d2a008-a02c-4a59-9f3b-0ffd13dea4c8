package com.example.ckc_englihoo.DataClass

// ==================== USER DATA CLASSES ====================
// NOTE: Student, Teacher, Course classes moved to separate files to avoid duplication

data class EnrollmentCounts(
    val pending: Int,
    val studying: Int,
    val passed: Int,
    val failed: Int
)

// ==================== LESSON DATA CLASSES ====================
// NOTE: Lesson, LessonPart classes moved to Lesson.kt to avoid duplication

// LessonPartWithProgress moved to Lesson.kt to avoid duplication

// ==================== QUESTION & ASSIGNMENT DATA CLASSES ====================

data class Assignment(
    val lesson_part_id: Int,
    val part_type: String,
    val level: String,
    val questions: List<Question>
)

data class Question(
    val questions_id: Int, // Primary field from API
    val lesson_part_id: Int,
    val question_type: String, // single_choice, matching, classification, fill_blank, arrangement, image_word
    val question_text: String,
    val media_url: String?,
    val order_index: Int,
    val created_at: String,
    val updated_at: String,
    val answers: List<Answer> = emptyList() // API returns as List<Answer>
)

data class Answer(
    val answers_id: Int,
    val questions_id: Int, // Updated to match API response
    val match_key: String?,
    val answer_text: String,
    val is_correct: Int, // 0: false, 1: true
    val feedback: String?,
    val media_url: String?,
    val order_index: Int,
    val created_at: String,
    val updated_at: String
)

// ==================== PROGRESS DATA CLASSES ====================

data class ProgressResponse<T>(
    val success: Boolean,
    val data: T
)



data class OverallProgress(
    val student_id: Int,
    val student_name: String,
    val total_courses: Int,
    val completed_courses: Int,
    val overall_progress_percentage: Double,
    val courses_progress: List<CourseProgress>
)

data class CourseProgress(
    val course_id: Int,
    val course_name: String,
    val student_id: Int,
    val enrollment_status: Int,
    val total_questions: Int,
    val answered_questions: Int,
    val correct_answers: Int,
    val overall_progress_percentage: Double,
    val correct_percentage: Double,
    val is_completed: Boolean,
    val required_correct_percentage: Int,
    val lessons_progress: List<LessonProgressItem>,
    val total_time_spent_minutes: Int,
    val estimated_completion_date: String?
)
data class CourseProgressResponse(
    val success: Boolean,
    val data: CourseProgress?
)

data class LessonProgressItem(
    val lesson_part_id: Int,
    val level: String,
    val lesson_title: String,
    val total_questions: Int,
    val answered_questions: Int,
    val correct_answers: Int,
    val progress_percentage: Double,
    val is_completed: Boolean
)

data class LessonPartProgress(
    val student_id: Int,
    val lesson_part_id: Int,
    val course_id: Int?,
    val lesson_part_title: String,
    val total_questions: Int,
    val answered_questions: Int,
    val correct_answers: Int,
    val progress_percentage: Double,
    val is_completed: Boolean,
    val required_correct_answers: Int
)

data class LessonProgress(
    val student_id: Int,
    val lesson_level: String,
    val course_id: Int?,
    val lesson_title: String,
    val total_parts: Int,
    val completed_parts: Int,
    val progress_percentage: Double,
    val is_completed: Boolean
)

// ==================== SCORE SUBMISSION ====================
// Moved to LessonPartScore.kt to avoid duplication

// ==================== STUDENT ANSWER ====================

data class StudentAnswerRequest(
    val student_id: Int,
    val question_id: Int,
    val course_id: Int,
    val answer_text: String
)

data class StudentAnswerResponse(
    val success: Boolean,
    val is_correct: Boolean,
    val correct_answer: String?,
    val feedback: String,
    val score_points: Int
)

data class StudentAnswer(
    val student_answers_id: Int,
    val student_id: Int,
    val question_id: Int,
    val course_id: Int,
    val answer_text: String,
    val answered_at: String,
    val created_at: String,
    val updated_at: String,
    val question: Question?,
    val course: Course?,
    val student: Student?
)

data class RecentSubmissionData(
    val score: Double? = null,
    val progress: Double? = null,
    val submission_time: String? = null,
    val answers_count: Int? = null,
    val total_questions: Int? = null,
    val correct_answers: Int? = null,
    val student_answers: List<StudentAnswerDetail>? = null
)

data class StudentAnswerDetail(
    val question_id: Int,
    val question_text: String,
    val student_answer: String,
    val correct_answer: String?,
    val is_correct: Boolean,
    val feedback: String?
)

// ==================== NOTIFICATIONS ====================

data class UnreadCountResponse(
    val unread_count: Int
)

// NotificationResponse moved to Notification.kt to avoid duplication

// ==================== EXAM RESULTS ====================

data class ExamResultRequest(
    val student_id: Int,
    val course_id: Int,
    val exam_date: String,
    val lisstening_score: Double,
    val reading_score: Double,
    val speaking_score: Double,
    val writing_score: Double,
    val overall_status: Int
)

data class ExamResultResponse(
    val success: Boolean,
    val message: String,
    val exam_result: ExamResult?
)

// ==================== STUDENT EVALUATIONS ====================

data class StudentEvaluationRequest(
    val student_id: Int,
    val progress_id: Int,
    val exam_result_id: Int,
    val evaluation_date: String,
    val final_status: Int,
    val remark: String
)

data class StudentEvaluationResponse(
    val success: Boolean,
    val message: String,
    val evaluation: StudentEvaluation?
)

// ==================== CLASS POSTS ====================

data class PostAuthor(
    // For student authors
    val student_id: Int? = null,
    val avatar: String? = null,

    // For teacher authors
    val teacher_id: Int? = null,

    // Common fields
    val fullname: String,
    val username: String,
    val password: String,
    val date_of_birth: String,
    val gender: Int,
    val email: String,
    val is_status: Int,
    val created_at: String,
    val updated_at: String
)



// ==================== STATISTICS ====================

data class OverviewStatisticsResponse(
    val overview: OverviewStatistics,
    val enrollment_status: EnrollmentStatusStats,
    val course_levels: Map<String, Int>,
    val recent_activity: RecentActivityStats
)

data class OverviewStatistics(
    val total_students: Int,
    val total_courses: Int,
    val total_enrollments: Int,
    val total_questions: Int,
    val students_with_progress: Int,
    val average_completion_rate: Double
)

data class EnrollmentStatusStats(
    val pending: Int,
    val active: Int,
    val completed: Int,
    val failed: Int
)

data class RecentActivityStats(
    val answers_last_7_days: Int,
    val scores_last_7_days: Int
)

data class CourseStatistics(
    val course_id: Int,
    val course_name: String,
    val level: String,
    val total_students: Int,
    val active_students: Int,
    val completed_students: Int,
    val failed_students: Int,
    val completion_rate: Double,
    val average_progress: Double
)

data class StudentPerformanceStatistics(
    val student_id: Int,
    val student_name: String,
    val total_courses: Int,
    val completed_courses: Int,
    val average_score: Double,
    val total_study_time: Int,
    val last_activity: String
)

// ==================== ENROLLMENT ====================

data class EnrollmentRequest(
    val student_id: Int,
    val course_id: Int
)

data class EnrollmentResponse(
    val success: Boolean,
    val message: String,
    val enrollment: CourseEnrollment?
)

data class EnrollmentStatusRequest(
    val status: Int
)

// ==================== UI MODELS ====================

// NOTE: ClassPostDisplayUI moved to UIModels.kt to avoid duplication

// ==================== CHANGE PASSWORD ====================
// Note: ChangePasswordRequest moved to CONTROLLER-BASED section below

data class ChangePasswordResponse(
    val success: Boolean,
    val message: String
)

// ==================== MISSING DATA CLASSES ====================

data class LessonPartDetails(
    val lesson_part_id: Int,
    val level: String,
    val part_type: String,
    val content: String,
    // Note: LessonPartContent table has been removed from database
    val total_questions: Int,
    val estimated_time_minutes: Int
)








data class DetailedProgressResponse(
    val student_id: Int,
    val total_courses: Int,
    val completed_courses: Int,
    val in_progress_courses: Int,
    val overall_progress_percentage: Double,
    val total_study_time_minutes: Int,
    val achievements_count: Int,
    val current_streak_days: Int
)

// NOTE: OverallProgressData is duplicate of OverallProgress - removed
// NOTE: CourseWithDetails is similar to Course with nested data - keeping for specific use cases

data class StudentProgress(
    val student_id: Int,
    val course_id: Int,
    val overall_progress_percentage: Double,
    val completed_lessons: Int,
    val total_lessons: Int,
    val last_activity: String
)

// ==================== UI DISPLAY MODELS ====================

// NOTE: CourseDisplayUI and NotificationDisplayUI moved to UIModels.kt to avoid duplication

// NOTE: LessonDisplayUI and LessonPartDisplayUI moved to UIModels.kt to avoid duplication

// ==================== NEW STUDENT ANSWER MANAGEMENT ====================

data class StudentAnswerItem(
    val question_id: Int,
    val answer_text: String,
    val is_correct: Boolean? = null
)

data class StudentAnswersUpdateRequest(
    val answers: List<StudentAnswerItem>
)

data class StudentAnswersUpdateResponse(
    val success: Boolean,
    val message: String,
    val data: StudentAnswersUpdateData? = null
)

data class StudentAnswersUpdateData(
    val updated_count: Int,
    val score: Double? = null,
    val progress: Double? = null
)

// ==================== CONTROLLER-BASED REQUEST/RESPONSE MODELS ====================

// Change Password Request (based on controller validation)
data class ChangePasswordRequest(
    val current_password: String,
    val new_password: String,
    val new_password_confirmation: String
)

// Smart Course Registration Request (based on controller validation)
data class SmartCourseRegistrationRequest(
    val level: String, // A1, A2, A3, TA2-6
    val schedule_preference: String? = null
)

// Student Update Request (based on controller validation)
data class StudentUpdateRequest(
    val fullname: String? = null,
    val email: String? = null,
    val phone: String? = null,
    val address: String? = null,
    val date_of_birth: String? = null
)

// Create Answer Request (based on controller validation)
data class CreateAnswerRequest(
    val questions_id: Int,
    val match_key: String = "", // Required field with default empty string
    val answer_text: String,
    val is_correct: Boolean,
    val order_index: Int? = null
)

data class RecentSubmissionResponse(
    val success: Boolean,
    val data: RecentSubmissionData? = null
)



// ==================== ENROLLMENT STATUS UPDATE ====================

data class EnrollmentStatusUpdateRequest(
    val status: Int // 0=Pending, 1=Active, 2=Completed, 3=Failed
)

// ==================== COMMON RESPONSE CLASSES ====================
// DeleteResponse is defined in ApiResponse.kt
