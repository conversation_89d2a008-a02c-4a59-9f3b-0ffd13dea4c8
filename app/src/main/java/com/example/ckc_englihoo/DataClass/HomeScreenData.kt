package com.example.ckc_englihoo.DataClass

// Course with teachers and lesson info (from API)
data class CourseWithDetails(
    val course_id: Int,
    val course_name: String,
    val level: String,
    val year: String,
    val description: String,
    val status: String,
    val starts_date: String,
    val created_at: String,
    val updated_at: String,
    val lesson: LessonInfo,
    val teachers: List<TeacherInfo>
)

data class LessonInfo(
    val level: String,
    val title: String,
    val description: String,
    val order_index: Int,
    val created_at: String,
    val updated_at: String
)

data class TeacherInfo(
    val teacher_id: Int,
    val fullname: String,
    val username: String,
    val date_of_birth: String,
    val gender: Int,
    val email: String,
    val is_status: Int,
    val created_at: String,
    val updated_at: String,
    val pivot: TeacherPivot
)

data class TeacherPivot(
    val course_id: Int,
    val teacher_id: Int,
    val role: String,
    val assigned_at: String
)

// Overall progress response
data class OverallProgressResponse(
    val success: Boolean,
    val data: OverallProgressData
)

data class OverallProgressData(
    val student_id: String,
    val student_name: String,
    val total_lessons: Int,
    val completed_lessons: Int,
    val overall_progress_percentage: Int,
    val lessons_progress: List<LessonProgressHome>
)

data class LessonProgressHome(
    val student_id: String,
    val lesson_level: String,
    val lesson_title: String,
    val total_parts: Int,
    val completed_parts: Int,
    val progress_percentage: Int,
    val is_completed: Boolean
)

// Lesson part with progress (from API) - renamed to avoid conflict
data class LessonPartWithProgressHome(
    val lesson_part_id: Int,
    val level: String,
    val part_type: String,
    val content: String,
    val order_index: Int,
    val is_completed: Boolean,
    val progress_percentage: Int,
    val best_score: Float?,
    val attempts_count: Int
)

// Note: CourseDisplayUI and NotificationDisplayUI are defined in UIModels.kt
