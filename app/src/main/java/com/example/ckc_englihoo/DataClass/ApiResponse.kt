package com.example.ckc_englihoo.DataClass

import com.google.gson.annotations.SerializedName

/**
 * Generic API response wrapper for Laravel API
 */
data class ApiResponse<T>(
    val success: Boolean,
    val message: String? = null,
    val data: T? = null,
    val error: String? = null
)

/**
 * Response for class post creation/update
 */
data class ClassPostResponse(
    val success: Boolean,
    val message: String,
    val data: ClassPost
)

/**
 * Response for class post comment creation/update
 */
data class ClassPostCommentResponse(
    val success: Boolean,
    val message: String,
    val data: ClassPostComment
)

/**
 * Response for delete operations
 */
data class DeleteResponse(
    val success: Boolean,
    val message: String
)

/**
 * Request body for creating class post
 */
data class CreateClassPostRequest(
    val course_id: Int,
    val teacher_id: Int,
    val title: String,
    val content: String
)

/**
 * Request body for updating class post
 */
data class UpdateClassPostRequest(
    val title: String? = null,
    val content: String? = null,
    val status: Int? = null
)

/**
 * Request body for creating class post comment
 */
data class CreateClassPostCommentRequest(
    val post_id: Int,
    val student_id: Int? = null,
    val teacher_id: Int? = null,
    val content: String
)

/**
 * Request body for updating class post comment
 */
data class UpdateClassPostCommentRequest(
    val content: String? = null,
    val status: Int? = null
)
