package com.example.ckc_englihoo.DataClass

import com.google.gson.annotations.SerializedName

/**
 * Data class for Teacher Assignment API response
 */
data class TeacherAssignmentResponse(
    @SerializedName("success") val success: Bo<PERSON>an,
    @SerializedName("data") val data: List<TeacherCourseAssignment>,
    @SerializedName("teacher_info") val teacherInfo: TeacherAssignmentInfo,
    @SerializedName("meta") val meta: AssignmentMeta
)

/**
 * Individual teacher course assignment
 */
data class TeacherCourseAssignment(
    @SerializedName("assignment_id") val assignmentId: Int,
    @SerializedName("role") val role: String,
    @SerializedName("assigned_at") val assignedAt: String,
    @SerializedName("course") val course: AssignedCourse
)

/**
 * Course information in assignment
 */
data class AssignedCourse(
    @SerializedName("course_id") val courseId: Int,
    @SerializedName("course_name") val courseName: String,
    @SerializedName("level") val level: String,
    @SerializedName("schedule") val schedule: String?
)

/**
 * Teacher information in assignment response
 */
data class TeacherAssignmentInfo(
    @SerializedName("teacher_id") val teacherId: Int,
    @SerializedName("fullname") val fullname: String,
    @SerializedName("email") val email: String
)

/**
 * Assignment metadata
 */
data class AssignmentMeta(
    @SerializedName("total_assignments") val totalAssignments: Int,
    @SerializedName("courses_count") val coursesCount: Int,
    @SerializedName("roles") val roles: List<String>
)
