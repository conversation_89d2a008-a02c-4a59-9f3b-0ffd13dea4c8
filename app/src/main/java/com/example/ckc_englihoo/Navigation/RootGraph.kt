package com.example.ckc_englihoo.Navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.navigation.NavHostController
import com.example.ckc_englihoo.Utils.extractStudentId
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import androidx.navigation.NavType
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.Screen.*
import com.example.ckc_englihoo.Components.CreateClassTeacher

// ==================== NAVIGATION ROUTES ====================
sealed class Screen(val route: String) {
    // Main Navigation
    object Splash : Screen("splash")
    object Onboarding : Screen("onboarding")
    object Login : Screen("login")
    object Main : Screen("main")

    // Student Screens
    object CourseRegistration : Screen("course_registration")
    object StudentProfile : Screen("student_profile")
    object Notifications : Screen("notifications")
    object ExamResults : Screen("exam_results")

    // Course & Lesson Screens
    object ClassStream : Screen("class_stream/{courseId}") {
        fun createRoute(courseId: Int) = "class_stream/$courseId"
    }
    object LessonParts : Screen("lesson_parts/{courseId}") {
        fun createRoute(courseId: Int) = "lesson_parts/$courseId"
    }
    object Questions : Screen("questions/{lessonPartId}/{lessonPartTitle}/{studentId}/{courseId}") {
        fun createRoute(lessonPartId: Int, lessonPartTitle: String, studentId: Int, courseId: Int) =
            "questions/$lessonPartId/$lessonPartTitle/$studentId/$courseId"
    }

    object Result : Screen("result/{studentId}/{courseId}/{lessonPartId}/{lessonPartTitle}/{submissionTime}") {
        fun createRoute(studentId: Int, courseId: Int, lessonPartId: Int, lessonPartTitle: String, submissionTime: String) =
            "result/$studentId/$courseId/$lessonPartId/$lessonPartTitle/$submissionTime"
    }
    object ExamResultDetail : Screen("exam_result_detail/{examId}") {
        fun createRoute(examId: Int) = "exam_result_detail/$examId"
    }

    // Teacher Screens
    object ClassDetailTeacher : Screen("class_detail_teacher/{courseId}") {
        fun createRoute(courseId: Int) = "class_detail_teacher/$courseId"
    }
    object GradesTeacher : Screen("grades_teacher/{courseId}") {
        fun createRoute(courseId: Int) = "grades_teacher/$courseId"
    }
    object StudentGradingTeacher : Screen("student_grading_teacher/{studentId}/{courseId}") {
        fun createRoute(studentId: Int, courseId: Int) = "student_grading_teacher/$studentId/$courseId"
    }
    object CreateClassTeacher : Screen("create_class_teacher")
    object MyProfileTeacher : Screen("my_profile_teacher")
}

// Legacy support
sealed class NavGraph(var route: String) {
    object Splash: NavGraph("splash")
    object Login: NavGraph("login")
    object Main: NavGraph("main")
}

// ==================== ROOT NAVIGATION GRAPH ====================
@Composable
fun RootGraph(navController: NavHostController, viewModel: AppViewModel) {
    NavHost(
        navController = navController,
        startDestination = Screen.Splash.route
    ) {
        // ==================== MAIN NAVIGATION ====================
        composable(Screen.Splash.route) {
            SplashScreen(navController = navController, viewModel = viewModel)
        }

        composable(Screen.Onboarding.route) {
            OnboardingScreen(navController = navController)
        }

        composable(Screen.Login.route) {
            LoginScreen(navController = navController, viewModel = viewModel)
        }

        composable(Screen.Main.route) {
            MainScreen(navRootController = navController, viewModel = viewModel)
        }

        // ==================== STUDENT SCREENS ====================
        composable(Screen.CourseRegistration.route) {
            CourseRegistrationScreen(
                navController = navController,
                viewModel = viewModel
            )
        }

        composable(Screen.StudentProfile.route) {
            StudentProfileScreen(
                navController = navController,
                viewModel = viewModel
            )
        }

        composable(Screen.Notifications.route) {
            NotificationScreen(
                navController = navController,
                viewModel = viewModel
            )
        }

        composable(Screen.ExamResults.route) {
            ExamResultsScreen(
                navController = navController,
                viewModel = viewModel
            )
        }

        // ==================== COURSE & LESSON SCREENS ====================
        composable(
            route = Screen.ClassStream.route,
            arguments = listOf(
                navArgument("courseId") { type = NavType.IntType }
            )
        ) { backStackEntry ->
            val courseId = backStackEntry.arguments?.getInt("courseId") ?: 1

            ClassStreamScreen(
                navController = navController,
                viewModel = viewModel,
                courseId = courseId
            )
        }

        composable(
            route = Screen.LessonParts.route,
            arguments = listOf(
                navArgument("courseId") { type = NavType.IntType }
            )
        ) { backStackEntry ->
            val courseId = backStackEntry.arguments?.getInt("courseId") ?: 0

            LessonPartsScreen(
                navController = navController,
                viewModel = viewModel,
                courseId = courseId
            )
        }

        composable(
            route = Screen.Questions.route,
            arguments = listOf(
                navArgument("lessonPartId") { type = NavType.StringType },
                navArgument("lessonPartTitle") { type = NavType.StringType },
                navArgument("studentId") { type = NavType.StringType },
                navArgument("courseId") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val lessonPartId = backStackEntry.arguments?.getString("lessonPartId")?.toIntOrNull() ?: 0
            val lessonPartTitle = backStackEntry.arguments?.getString("lessonPartTitle") ?: ""
            val studentId = extractStudentId(backStackEntry.arguments?.getString("studentId"))
            val courseId = backStackEntry.arguments?.getString("courseId")?.toIntOrNull() ?: 0

            QuestionScreenWithTabs(
                navController = navController,
                lessonPartId = lessonPartId,
                lessonPartTitle = lessonPartTitle,
                studentId = studentId,
                courseId = courseId,
                viewModel = viewModel
            )
        }

        composable(
            route = Screen.Result.route,
            arguments = listOf(
                navArgument("studentId") { type = NavType.StringType },
                navArgument("courseId") { type = NavType.StringType },
                navArgument("lessonPartId") { type = NavType.StringType },
                navArgument("lessonPartTitle") { type = NavType.StringType },
                navArgument("submissionTime") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val studentId = backStackEntry.arguments?.getString("studentId")?.toIntOrNull() ?: 0
            val courseId = backStackEntry.arguments?.getString("courseId")?.toIntOrNull() ?: 0
            val lessonPartId = backStackEntry.arguments?.getString("lessonPartId")?.toIntOrNull() ?: 0
            val lessonPartTitle = backStackEntry.arguments?.getString("lessonPartTitle") ?: ""
            val submissionTime = backStackEntry.arguments?.getString("submissionTime") ?: ""

            ResultScreen(
                navController = navController,
                studentId = studentId,
                courseId = courseId,
                lessonPartId = lessonPartId,
                lessonPartTitle = lessonPartTitle,
                submissionTime = submissionTime,
                viewModel = viewModel
            )
        }

        composable(
            route = Screen.ExamResultDetail.route,
            arguments = listOf(navArgument("examId") { type = NavType.IntType })
        ) { backStackEntry ->
            val examId = backStackEntry.arguments?.getInt("examId") ?: 0

            ExamResultDetailScreen(
                navController = navController,
                viewModel = viewModel,
                examId = examId
            )
        }

        // ==================== TEACHER SCREENS ====================
        composable(
            route = Screen.ClassDetailTeacher.route,
            arguments = listOf(navArgument("courseId") { type = NavType.StringType })
        ) { backStackEntry ->
            val courseId = backStackEntry.arguments?.getString("courseId")?.toIntOrNull() ?: 0
            val courses by viewModel.teacherCourses.collectAsState()
            val course = courses.find { it.course_id == courseId }

            if (course != null) {
                ClassDetailTeacher(
                    navController = navController,
                    viewModel = viewModel,
                    course = course
                )
            }
        }

        composable(
            route = Screen.GradesTeacher.route,
            arguments = listOf(navArgument("courseId") { type = NavType.StringType })
        ) { backStackEntry ->
            val courseId = backStackEntry.arguments?.getString("courseId")?.toIntOrNull() ?: 0

            GradesTeacher(
                navController = navController,
                viewModel = viewModel,
                courseId = courseId
            )
        }

        composable(
            route = Screen.StudentGradingTeacher.route,
            arguments = listOf(
                navArgument("studentId") { type = NavType.StringType },
                navArgument("courseId") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val studentId = extractStudentId(backStackEntry.arguments?.getString("studentId"))
            val courseId = backStackEntry.arguments?.getString("courseId")?.toIntOrNull() ?: 0

            StudentGradingTeacher(
                navController = navController,
                viewModel = viewModel,
                studentId = studentId,
                courseId = courseId
            )
        }

        composable(Screen.CreateClassTeacher.route) {
            CreateClassTeacher(
                onNavigateBack = { navController.popBackStack() }
            )
        }

        composable(Screen.MyProfileTeacher.route) {
            MyProfileTeacher(
                navController = navController,
                viewModel = viewModel
            )
        }
    }
}