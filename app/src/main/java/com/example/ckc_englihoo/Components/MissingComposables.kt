package com.example.ckc_englihoo.Components

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

// Missing Composables for WordOrderExercise
@Composable
fun WordOrderQuestionCounter(
    currentQuestion: Int,
    totalQuestions: Int,
    modifier: Modifier = Modifier
) {
    Text(
        text = "Question $currentQuestion of $totalQuestions",
        fontSize = 16.sp,
        fontWeight = FontWeight.Medium,
        modifier = modifier
    )
}

@Composable
fun WordOrderGameDisplay(
    questionText: String,
    selectedWords: List<String>,
    availableWords: List<String>,
    onWordSelected: (String) -> Unit,
    onWordRemoved: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Question text
        Text(
            text = questionText,
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        // Selected words area
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .height(80.dp)
                .padding(bottom = 16.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(8.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                selectedWords.forEach { word ->
                    Button(
                        onClick = { onWordRemoved(word) },
                        modifier = Modifier.height(40.dp)
                    ) {
                        Text(word)
                    }
                }
            }
        }
        
        // Available words
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            availableWords.forEach { word ->
                Button(
                    onClick = { onWordSelected(word) },
                    modifier = Modifier.height(40.dp)
                ) {
                    Text(word)
                }
            }
        }
    }
}

// Missing Composable for CreateClassTeacher
@Composable
fun CreateClassTeacher(
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "Create Class Teacher",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "This feature is under development",
            fontSize = 16.sp
        )
        
        Spacer(modifier = Modifier.height(32.dp))
        
        Button(
            onClick = onNavigateBack
        ) {
            Text("Go Back")
        }
    }
}
