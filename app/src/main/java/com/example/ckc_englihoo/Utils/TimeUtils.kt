package com.example.ckc_englihoo.Utils

import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimeZone

/**
 * Utility functions for time formatting
 */

/**
 * Format a Date to a human-readable "time ago" string
 * @param date The date to format
 * @return Formatted string like "2h trước", "Hôm qua", etc.
 */
fun formatTimeAgo(date: Date): String {
    val now = Date()
    val diffInMillis = now.time - date.time
    val diffInHours = diffInMillis / (1000 * 60 * 60)
    val diffInDays = diffInHours / 24

    return when {
        diffInHours < 1 -> "Vừa xong"
        diffInHours < 24 -> "${diffInHours}h trước"
        diffInDays == 1L -> "Hôm qua"
        diffInDays < 7 -> "${diffInDays} ngày trước"
        else -> {
            val dateFormat = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault())
            dateFormat.format(date)
        }
    }
}

/**
 * Format a String date to a human-readable "time ago" string
 * @param dateString The date string to format (ISO format from API)
 * @return Formatted string like "2h trước", "Hôm qua", etc.
 */
fun formatTimeAgo(dateString: String): String {
    return try {
        // 1. Chuẩn hóa: bỏ phần micro/nano giây trước 'Z'
        //    ví dụ "2025-06-12T02:29:04.000000Z" -> "2025-06-12T02:29:04Z"
        val cleaned = dateString.replace(Regex("\\.\\d+Z\$"), "Z")

        // 2. Parse bằng ISO format
        val isoFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.getDefault())
        isoFormat.timeZone = TimeZone.getTimeZone("UTC")
        val date = isoFormat.parse(cleaned)

        // 3. Nếu parse thành công, delegate về hàm xử lý Date
        if (date != null) {
            formatTimeAgo(date)
        } else {
            "Không xác định"
        }
    } catch (e: Exception) {
        // Bất kỳ lỗi parse nào cũng trả về "Không xác định"
        "Không xác định"
    }
}

/**
 * Format a Date to a specific format
 * @param date The date to format
 * @param pattern The pattern to use (default: "dd/MM/yyyy")
 * @return Formatted date string
 */
fun formatDate(date: Date, pattern: String = "dd/MM/yyyy"): String {
    val dateFormat = SimpleDateFormat(pattern, Locale.getDefault())
    return dateFormat.format(date)
}

/**
 * Format a Date to time only
 * @param date The date to format
 * @return Formatted time string like "14:30"
 */
fun formatTime(date: Date): String {
    val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
    return timeFormat.format(date)
}

/**
 * Format a Date to full date and time
 * @param date The date to format
 * @return Formatted string like "25/12/2023 14:30"
 */
fun formatDateTime(date: Date): String {
    val dateTimeFormat = SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault())
    return dateTimeFormat.format(date)
}
