package com.example.ckc_englihoo.Utils

import androidx.compose.ui.graphics.Color

// Missing Colors for WordOrderExercise
object WordOrderColors {
    val primary = Color(0xFF6200EE)
    val secondary = Color(0xFF03DAC6)
    val background = Color(0xFFF5F5F5)
    val surface = Color.White
    val onPrimary = Color.White
    val onSecondary = Color.Black
    val onBackground = Color.Black
    val onSurface = Color.Black
    val correct = Color(0xFF4CAF50)
    val incorrect = Color(0xFFF44336)
    val selected = Color(0xFF2196F3)
    val unselected = Color(0xFFE0E0E0)
}

// Missing functions for LoginScreen
object LoginUtils {
    fun getSavedUsername(): String {
        // Placeholder implementation
        return ""
    }
    
    fun getSavedPassword(): String {
        // Placeholder implementation
        return ""
    }
    
    fun isRememberLogin(): Boolean {
        // Placeholder implementation
        return false
    }
    
    fun checkSavedLogin(): Boolean {
        // Placeholder implementation
        return false
    }
}

// Missing function for AppViewModel
fun changePassword(viewModel: Any, studentId: Int, oldPassword: String, newPassword: String) {
    // Placeholder implementation
    // This should call the API to change password
}

// Utility function to extract student ID from string that might contain prefix
fun extractStudentId(studentIdString: String?): Int {
    if (studentIdString.isNullOrEmpty()) return 0

    return try {
        // If it's already a number, parse it directly
        studentIdString.toInt()
    } catch (e: NumberFormatException) {
        try {
            // If it contains "student_" prefix, extract the number part
            if (studentIdString.contains("student_")) {
                val numberPart = studentIdString.substringAfter("student_")
                numberPart.toInt()
            } else {
                // Try to extract any number from the string
                val numberRegex = "\\d+".toRegex()
                val match = numberRegex.find(studentIdString)
                match?.value?.toInt() ?: 0
            }
        } catch (e: NumberFormatException) {
            0
        }
    }
}
