package com.example.ckc_englihoo.Utils

import com.example.ckc_englihoo.DataClass.Question
import com.example.ckc_englihoo.DataClass.StudentAnswerDetail

/**
 * ResultFormatter - Utility class để format kết quả hiển thị
 * <PERSON>y<PERSON>n đổi từ QuestionResult sang StudentAnswerDetail và format display
 */
object ResultFormatter {

    /**
     * Chuyển đổi QuestionR<PERSON>ult sang StudentAnswerDetail để hiển thị
     */
    fun formatQuestionResult(
        result: QuestionResult,
        question: Question
    ): StudentAnswerDetail {
        return StudentAnswerDetail(
            question_id = result.questionId,
            question_text = question.question_text,
            student_answer = formatStudentAnswerDisplay(result, question),
            correct_answer = result.correctAnswer,
            is_correct = result.isCorrect,
            feedback = result.explanation
        )
    }

    /**
     * Format student answer để hiển thị dễ hiểu
     */
    private fun formatStudentAnswerDisplay(
        result: QuestionResult,
        question: Question
    ): String {
        return when (question.question_type) {
            "single_choice" -> formatSingleChoiceDisplay(result, question)
            "matching" -> formatMatchingDisplay(result, question)
            "classification" -> formatClassificationDisplay(result, question)
            "fill_blank" -> result.selectedAnswer
            "arrangement" -> formatArrangementDisplay(result, question)
            "image_word" -> result.selectedAnswer
            else -> result.selectedAnswer
        }
    }

    /**
     * Format single choice display
     */
    private fun formatSingleChoiceDisplay(
        result: QuestionResult,
        question: Question
    ): String {
        val answerId = result.normalizedAnswer.toIntOrNull()
        val answer = question.answers.find { it.answers_id == answerId }
        return answer?.answer_text ?: result.selectedAnswer
    }

    /**
     * Format matching display
     */
    private fun formatMatchingDisplay(
        result: QuestionResult,
        question: Question
    ): String {
        try {
            val pairs = result.normalizedAnswer.split(",")
            return pairs.mapNotNull { pair ->
                val parts = pair.split(":")
                if (parts.size == 2) {
                    val leftId = parts[0].toIntOrNull()
                    val rightId = parts[1].toIntOrNull()
                    
                    val leftText = question.answers.find { it.answers_id == leftId }?.answer_text
                    val rightText = question.answers.find { it.answers_id == rightId }?.answer_text
                    
                    if (leftText != null && rightText != null) {
                        "$leftText → $rightText"
                    } else null
                } else null
            }.joinToString(", ")
        } catch (e: Exception) {
            return result.selectedAnswer
        }
    }

    /**
     * Format classification display
     */
    private fun formatClassificationDisplay(
        result: QuestionResult,
        question: Question
    ): String {
        try {
            val classifications = result.normalizedAnswer.split(",")
            return classifications.mapNotNull { classification ->
                val parts = classification.split(":")
                if (parts.size == 2) {
                    val wordId = parts[0].toIntOrNull()
                    val category = parts[1]
                    
                    val wordText = question.answers.find { it.answers_id == wordId }?.answer_text
                    
                    if (wordText != null) {
                        "$wordText → $category"
                    } else null
                } else null
            }.joinToString(", ")
        } catch (e: Exception) {
            return result.selectedAnswer
        }
    }

    /**
     * Format arrangement display
     */
    private fun formatArrangementDisplay(
        result: QuestionResult,
        question: Question
    ): String {
        try {
            val wordIds = result.normalizedAnswer.split(",").mapNotNull { it.toIntOrNull() }
            return wordIds.mapNotNull { wordId ->
                question.answers.find { it.answers_id == wordId }?.answer_text
            }.joinToString(" ")
        } catch (e: Exception) {
            return result.selectedAnswer
        }
    }

    /**
     * Format score display
     */
    fun formatScoreDisplay(score: Double): String {
        return "${(score * 10).toInt()}/10"
    }

    /**
     * Format percentage display
     */
    fun formatPercentageDisplay(percentage: Double): String {
        return "${percentage.toInt()}%"
    }

    /**
     * Get status text based on score
     */
    fun getStatusText(score: Double): String {
        return if (score >= 7.0) "Đạt" else "Chưa đạt"
    }

    /**
     * Get status color based on score
     */
    fun getStatusColor(score: Double): androidx.compose.ui.graphics.Color {
        return if (score >= 7.0) {
            androidx.compose.ui.graphics.Color(0xFF4CAF50) // Green
        } else {
            androidx.compose.ui.graphics.Color(0xFFF44336) // Red
        }
    }

    /**
     * Format detailed explanation cho từng loại câu hỏi
     */
    fun formatDetailedExplanation(
        result: QuestionResult,
        question: Question
    ): String {
        val baseExplanation = result.explanation
        
        return when (question.question_type) {
            "single_choice" -> {
                val correctAnswer = question.answers.find { it.is_correct == 1 }
                "Đáp án đúng: ${correctAnswer?.answer_text ?: "Không xác định"}"
            }
            "matching" -> {
                val correctPairs = question.answers.filter { it.is_correct == 1 }
                val correctDisplay = correctPairs.map { "${it.match_key} → ${it.answer_text}" }
                "Cặp nối đúng:\n${correctDisplay.joinToString("\n")}"
            }
            "classification" -> {
                val correctClassifications = question.answers.filter { it.is_correct == 1 }
                val correctDisplay = correctClassifications.map { "${it.answer_text} → ${it.match_key}" }
                "Phân loại đúng:\n${correctDisplay.joinToString("\n")}"
            }
            "arrangement" -> {
                val correctOrder = question.answers
                    .filter { it.is_correct == 1 }
                    .sortedBy { it.order_index }
                    .map { it.answer_text }
                "Thứ tự đúng: ${correctOrder.joinToString(" ")}"
            }
            "fill_blank" -> {
                val correctAnswers = question.answers.filter { it.is_correct == 1 }
                "Đáp án đúng: ${correctAnswers.joinToString(" / ") { it.answer_text }}"
            }
            "image_word" -> {
                val correctLetters = question.answers
                    .filter { it.is_correct == 1 }
                    .sortedBy { it.order_index }
                    .map { it.answer_text }
                "Từ đúng: ${correctLetters.joinToString("")}"
            }
            else -> baseExplanation
        }
    }

    /**
     * Format summary statistics
     */
    fun formatSummaryStats(
        totalQuestions: Int,
        correctAnswers: Int,
        score: Double
    ): String {
        val percentage = (correctAnswers.toDouble() / totalQuestions.toDouble() * 100).toInt()
        return "Trả lời đúng: $correctAnswers/$totalQuestions câu ($percentage%)\n" +
                "Điểm số: ${formatScoreDisplay(score)}\n" +
                "Kết quả: ${getStatusText(score)}"
    }

    /**
     * Format time display
     */
    fun formatTimeDisplay(timeString: String): String {
        return try {
            // Assuming timeString is in format "yyyy-MM-dd HH:mm:ss"
            val parts = timeString.split(" ")
            if (parts.size >= 2) {
                val datePart = parts[0]
                val timePart = parts[1].substring(0, 5) // HH:mm
                "$datePart lúc $timePart"
            } else {
                timeString
            }
        } catch (e: Exception) {
            timeString
        }
    }
}
