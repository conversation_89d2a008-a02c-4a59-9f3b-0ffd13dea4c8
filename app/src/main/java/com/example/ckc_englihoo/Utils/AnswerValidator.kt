package com.example.ckc_englihoo.Utils

import com.example.ckc_englihoo.DataClass.Question
import com.example.ckc_englihoo.DataClass.StudentAnswerItem

/**
 * AnswerValidator - Utility class để validate và format đáp án
 * Hỗ trợ validation trước khi submit và format display
 */
object AnswerValidator {

    /**
     * Validate đáp án trướ<PERSON> khi submit
     */
    fun validateAnswer(question: Question, answerText: String): ValidationResult {
        return when (question.question_type) {
            "single_choice" -> validateSingleChoice(question, answerText)
            "matching" -> validateMatching(question, answerText)
            "classification" -> validateClassification(question, answerText)
            "fill_blank" -> validateFillBlank(answerText)
            "arrangement" -> validateArrangement(question, answerText)
            "image_word" -> validateImageWord(answerText)
            else -> ValidationResult(false, "<PERSON><PERSON><PERSON> câu hỏi không được hỗ trợ")
        }
    }

    /**
     * Validate single choice answer
     */
    private fun validateSingleChoice(question: Question, answerText: String): ValidationResult {
        if (answerText.isBlank()) {
            return ValidationResult(false, "Vui lòng chọn một đáp án")
        }
        
        val answerId = answerText.toIntOrNull()
        if (answerId == null) {
            return ValidationResult(false, "Định dạng đáp án không hợp lệ")
        }
        
        val answerExists = question.answers.any { it.answers_id == answerId }
        if (!answerExists) {
            return ValidationResult(false, "Đáp án không tồn tại")
        }
        
        return ValidationResult(true, "Hợp lệ")
    }

    /**
     * Validate matching answer
     */
    private fun validateMatching(question: Question, answerText: String): ValidationResult {
        if (answerText.isBlank()) {
            return ValidationResult(false, "Vui lòng nối ít nhất một cặp")
        }
        
        try {
            val pairs = answerText.split(",")
            if (pairs.isEmpty()) {
                return ValidationResult(false, "Không có cặp nào được nối")
            }
            
            pairs.forEach { pair ->
                val parts = pair.split(":")
                if (parts.size != 2) {
                    return ValidationResult(false, "Định dạng cặp nối không hợp lệ")
                }
                
                val leftId = parts[0].toIntOrNull()
                val rightId = parts[1].toIntOrNull()
                
                if (leftId == null || rightId == null) {
                    return ValidationResult(false, "ID đáp án không hợp lệ")
                }
                
                val leftExists = question.answers.any { it.answers_id == leftId }
                val rightExists = question.answers.any { it.answers_id == rightId }
                
                if (!leftExists || !rightExists) {
                    return ValidationResult(false, "Đáp án không tồn tại")
                }
            }
            
            return ValidationResult(true, "Hợp lệ")
        } catch (e: Exception) {
            return ValidationResult(false, "Lỗi định dạng đáp án matching")
        }
    }

    /**
     * Validate classification answer
     */
    private fun validateClassification(question: Question, answerText: String): ValidationResult {
        if (answerText.isBlank()) {
            return ValidationResult(false, "Vui lòng phân loại ít nhất một từ")
        }
        
        try {
            val classifications = answerText.split(",")
            if (classifications.isEmpty()) {
                return ValidationResult(false, "Không có từ nào được phân loại")
            }
            
            classifications.forEach { classification ->
                val parts = classification.split(":")
                if (parts.size != 2) {
                    return ValidationResult(false, "Định dạng phân loại không hợp lệ")
                }
                
                val wordId = parts[0].toIntOrNull()
                val category = parts[1].trim()
                
                if (wordId == null) {
                    return ValidationResult(false, "ID từ không hợp lệ")
                }
                
                if (category.isBlank()) {
                    return ValidationResult(false, "Danh mục không được để trống")
                }
                
                val wordExists = question.answers.any { it.answers_id == wordId }
                if (!wordExists) {
                    return ValidationResult(false, "Từ không tồn tại")
                }
            }
            
            return ValidationResult(true, "Hợp lệ")
        } catch (e: Exception) {
            return ValidationResult(false, "Lỗi định dạng đáp án classification")
        }
    }

    /**
     * Validate fill blank answer
     */
    private fun validateFillBlank(answerText: String): ValidationResult {
        if (answerText.isBlank()) {
            return ValidationResult(false, "Vui lòng nhập câu trả lời")
        }
        
        if (answerText.trim().length < 1) {
            return ValidationResult(false, "Câu trả lời quá ngắn")
        }
        
        return ValidationResult(true, "Hợp lệ")
    }

    /**
     * Validate arrangement answer
     */
    private fun validateArrangement(question: Question, answerText: String): ValidationResult {
        if (answerText.isBlank()) {
            return ValidationResult(false, "Vui lòng sắp xếp các từ")
        }
        
        try {
            val wordIds = answerText.split(",").mapNotNull { it.trim().toIntOrNull() }
            if (wordIds.isEmpty()) {
                return ValidationResult(false, "Không có từ nào được sắp xếp")
            }
            
            // Kiểm tra tất cả ID có tồn tại không
            wordIds.forEach { wordId ->
                val wordExists = question.answers.any { it.answers_id == wordId }
                if (!wordExists) {
                    return ValidationResult(false, "Từ không tồn tại")
                }
            }
            
            return ValidationResult(true, "Hợp lệ")
        } catch (e: Exception) {
            return ValidationResult(false, "Lỗi định dạng đáp án arrangement")
        }
    }

    /**
     * Validate image word answer
     */
    private fun validateImageWord(answerText: String): ValidationResult {
        if (answerText.isBlank()) {
            return ValidationResult(false, "Vui lòng nhập từ")
        }
        
        if (answerText.trim().length < 2) {
            return ValidationResult(false, "Từ quá ngắn")
        }
        
        // Kiểm tra chỉ chứa chữ cái
        if (!answerText.trim().matches(Regex("^[a-zA-Z]+$"))) {
            return ValidationResult(false, "Từ chỉ được chứa chữ cái")
        }
        
        return ValidationResult(true, "Hợp lệ")
    }

    /**
     * Validate tất cả đáp án trong một lesson part
     */
    fun validateAllAnswers(
        questions: List<Question>,
        answers: Map<Int, StudentAnswerItem>
    ): List<ValidationResult> {
        return questions.map { question ->
            val answer = answers[question.questions_id]
            if (answer != null) {
                validateAnswer(question, answer.answer_text)
            } else {
                ValidationResult(false, "Chưa trả lời câu hỏi")
            }
        }
    }

    /**
     * Kiểm tra xem có thể submit không
     */
    fun canSubmit(
        questions: List<Question>,
        answers: Map<Int, StudentAnswerItem>
    ): Boolean {
        val validationResults = validateAllAnswers(questions, answers)
        return validationResults.all { it.isValid }
    }

    /**
     * Lấy danh sách lỗi validation
     */
    fun getValidationErrors(
        questions: List<Question>,
        answers: Map<Int, StudentAnswerItem>
    ): List<String> {
        val validationResults = validateAllAnswers(questions, answers)
        return validationResults.mapIndexedNotNull { index, result ->
            if (!result.isValid) {
                "Câu ${index + 1}: ${result.message}"
            } else null
        }
    }
}

/**
 * Data class chứa kết quả validation
 */
data class ValidationResult(
    val isValid: Boolean,
    val message: String
)
