package com.example.ckc_englihoo.Utils

import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.ckc_englihoo.DataClass.Student
import com.example.ckc_englihoo.DataClass.Teacher
import com.example.ckc_englihoo.R

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TopSection(
    title: String,
    onBackClick: () -> Unit = {},
    showBackButton: Boolean = false,
    backgroundColor: Color = Color(0xFF1976D2),
    textColor: Color = Color.White,
    centerTitle: Boolean = false,
    actions: @Composable RowScope.() -> Unit = {}
) {
    TopAppBar(
        title = {
            Text(
                text = title,
                color = textColor,
                fontWeight = FontWeight.Bold,
                fontSize = 20.sp,
                textAlign = if (centerTitle) TextAlign.Center else TextAlign.Start,
                modifier = if (centerTitle) Modifier.fillMaxWidth() else Modifier
            )
        },
        navigationIcon = {
            if (showBackButton) {
                IconButton(onClick = onBackClick) {
                    Icon(
                        Icons.AutoMirrored.Filled.ArrowBack,
                        contentDescription = "Back",
                        tint = textColor
                    )
                }
            }
        },
        actions = actions,
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = backgroundColor
        )
    )
}

@Composable
fun StudentTopSection(
    student: Student?,
    title: String = "Trang chủ",
    onProfileClick: () -> Unit = {},
    onLogoutClick: () -> Unit = {},
    backgroundColor: Color = Color(0xFF1976D2) // Xanh dương đậm cho TopAppBar
) {
    Card(
        Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(bottomStart = 20.dp, bottomEnd = 20.dp),
        colors = CardDefaults.cardColors(containerColor = backgroundColor)
    ) {
        Column(
            Modifier.padding(20.dp)
        ) {
            // Header with profile
            Row(
                Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        title,
                        fontSize = 24.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                    student?.let {
                        Text(
                            "Xin chào, ${it.fullname}",
                            fontSize = 16.sp,
                            color = Color.White.copy(alpha = 0.9f)
                        )
                    }
                }

                // Profile section with dropdown
                ProfileSection(
                    student = student,
                    onProfileClick = onProfileClick,
                    onLogoutClick = onLogoutClick
                )
            }

            Spacer(Modifier.height(16.dp))

            // Student info card
            student?.let { studentData ->
                StudentInfoCard(student = studentData)
            }
        }
    }
}

@Composable
private fun ProfileSection(
    student: Student?,
    onProfileClick: () -> Unit,
    onLogoutClick: () -> Unit
) {
    var showDropdown by remember { mutableStateOf(false) }

    Box {
        // Avatar - clickable to show dropdown
        Box(
            Modifier
                .size(50.dp)
                .clip(CircleShape)
                .background(Color.White.copy(alpha = 0.2f))
                .clickable { showDropdown = true }
        ) {
            Image(
                painterResource(R.drawable.student),
                contentDescription = "Profile",
                modifier = Modifier.fillMaxSize()
            )
        }

        // Dropdown menu
        DropdownMenu(
            expanded = showDropdown,
            onDismissRequest = { showDropdown = false }
        ) {
            DropdownMenuItem(
                text = { Text("Hồ sơ sinh viên") },
                onClick = {
                    showDropdown = false
                    onProfileClick()
                },
                leadingIcon = {
                    Icon(
                        Icons.Default.Person,
                        contentDescription = null,
                        tint = Color(0xFF5BA3F5) // Màu xanh dương nhạt
                    )
                }
            )
            DropdownMenuItem(
                text = { Text("Đăng xuất") },
                onClick = {
                    showDropdown = false
                    onLogoutClick()
                },
                leadingIcon = {
                    Icon(
                        Icons.Default.ExitToApp,
                        contentDescription = null,
                        tint = Color(0xFFD32F2F) // Màu đỏ
                    )
                }
            )
        }
    }
}

@Composable
private fun StudentInfoCard(student: Student) {
    Card(
        Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.1f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            Modifier.padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(Modifier.weight(1f)) {
                Text(
                    "MSSV: ${student.email.substringBefore("@")}",
                    fontSize = 14.sp,
                    color = Color.White.copy(alpha = 0.9f),
                    fontWeight = FontWeight.Medium
                )
                Text(
                    "Ngày sinh: ${student.date_of_birth}",
                    fontSize = 12.sp,
                    color = Color.White.copy(alpha = 0.8f)
                )
            }

            Icon(
                Icons.Default.School,
                contentDescription = null,
                tint = Color.White.copy(alpha = 0.7f),
                modifier = Modifier.size(32.dp)
            )
        }
    }
}

@Composable
fun ProgressSection(
    overallProgress: Double,
    completedCourses: Int,
    totalCourses: Int,
    backgroundColor: Color = Color(0xFF5BA3F5) // Xanh dương nhạt gợi nhớ
) {
    Card(
        Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            Modifier.padding(20.dp)
        ) {
            Text(
                "Tiến độ học tập",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF333333)
            )

            Spacer(Modifier.height(16.dp))

            // Progress bar
            Column {
                Row(
                    Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        "Tiến độ tổng thể",
                        fontSize = 14.sp,
                        color = Color(0xFF666666)
                    )
                    Text(
                        "${overallProgress.toInt()}%",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Bold,
                        color = backgroundColor
                    )
                }

                Spacer(Modifier.height(8.dp))

                LinearProgressIndicator(
                    progress = (overallProgress / 100).toFloat(),
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(8.dp)
                        .clip(RoundedCornerShape(4.dp)),
                    color = backgroundColor,
                    trackColor = Color(0xFFE0E0E0)
                )
            }

            Spacer(Modifier.height(16.dp))

            // Course stats
            Row(
                Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatItem(
                    value = completedCourses.toString(),
                    label = "Hoàn thành",
                    color = Color(0xFF4CAF50)
                )
                StatItem(
                    value = totalCourses.toString(),
                    label = "Tổng khóa học",
                    color = backgroundColor
                )
                StatItem(
                    value = "${totalCourses - completedCourses}",
                    label = "Đang học",
                    color = Color(0xFFFF9800)
                )
            }
        }
    }
}

@Composable
private fun StatItem(
    value: String,
    label: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            value,
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            label,
            fontSize = 12.sp,
            color = Color(0xFF666666)
        )
    }
}

@Composable
fun TeacherTopSection(
    teacher: Teacher?,
    title: String = "Trang chủ",
    onProfileClick: () -> Unit = {},
    onLogoutClick: () -> Unit = {},
    backgroundColor: Color = Color(0xFF2E7D32) // Green color for teacher
) {
    Card(
        Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(bottomStart = 20.dp, bottomEnd = 20.dp),
        colors = CardDefaults.cardColors(containerColor = backgroundColor)
    ) {
        Column(
            Modifier.padding(20.dp)
        ) {
            // Header with profile
            Row(
                Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        title,
                        fontSize = 24.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                    teacher?.let {
                        Text(
                            "Xin chào, ${it.fullname}",
                            fontSize = 16.sp,
                            color = Color.White.copy(alpha = 0.9f)
                        )
                    }
                }

                // Profile section with dropdown
                TeacherProfileSection(
                    teacher = teacher,
                    onProfileClick = onProfileClick,
                    onLogoutClick = onLogoutClick
                )
            }

            Spacer(Modifier.height(16.dp))

            // Teacher info card
            teacher?.let { teacherData ->
                TeacherInfoCard(teacher = teacherData)
            }
        }
    }
}

@Composable
private fun TeacherProfileSection(
    teacher: Teacher?,
    onProfileClick: () -> Unit,
    onLogoutClick: () -> Unit
) {
    var showDropdown by remember { mutableStateOf(false) }

    Box {
        // Avatar - clickable to show dropdown
        Box(
            Modifier
                .size(50.dp)
                .clip(CircleShape)
                .background(Color.White.copy(alpha = 0.2f))
                .clickable { showDropdown = true }
        ) {
            Icon(
                Icons.Default.Person,
                contentDescription = "Profile",
                modifier = Modifier.fillMaxSize().padding(8.dp),
                tint = Color.White
            )
        }

        // Dropdown menu
        DropdownMenu(
            expanded = showDropdown,
            onDismissRequest = { showDropdown = false }
        ) {
            DropdownMenuItem(
                text = { Text("Hồ sơ giáo viên") },
                onClick = {
                    showDropdown = false
                    onProfileClick()
                },
                leadingIcon = {
                    Icon(
                        Icons.Default.Person,
                        contentDescription = null,
                        tint = Color(0xFF5BA3F5) // Màu xanh dương nhạt
                    )
                }
            )
            DropdownMenuItem(
                text = { Text("Đăng xuất") },
                onClick = {
                    showDropdown = false
                    onLogoutClick()
                },
                leadingIcon = {
                    Icon(
                        Icons.Default.ExitToApp,
                        contentDescription = null,
                        tint = Color(0xFFD32F2F) // Màu đỏ
                    )
                }
            )
        }
    }
}

@Composable
private fun TeacherInfoCard(teacher: Teacher) {
    Card(
        Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.1f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            Modifier.padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(Modifier.weight(1f)) {
                Text(
                    "Email: ${teacher.email}",
                    fontSize = 14.sp,
                    color = Color.White.copy(alpha = 0.9f),
                    fontWeight = FontWeight.Medium
                )
                Text(
                    "Ngày sinh: ${teacher.date_of_birth ?: "Chưa cập nhật"}",
                    fontSize = 12.sp,
                    color = Color.White.copy(alpha = 0.8f)
                )
            }

            Icon(
                Icons.Default.School,
                contentDescription = null,
                tint = Color.White.copy(alpha = 0.7f),
                modifier = Modifier.size(32.dp)
            )
        }
    }
}
