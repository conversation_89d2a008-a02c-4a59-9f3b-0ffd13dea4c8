package com.example.ckc_englihoo.Utils

import com.example.ckc_englihoo.DataClass.Question
import com.example.ckc_englihoo.DataClass.Answer
import com.example.ckc_englihoo.DataClass.StudentAnswerItem

/**
 * QuestionHandler - Xử lý đánh giá đáp án cho từng loại câu hỏi
 * Dựa trên tài liệu "Xử lý đánh giá đáp án cho từng loại câu hỏi.pdf"
 *
 * Cải thiện:
 * - Chuẩn hóa format đáp án cho từng loại câu hỏi
 * - Thêm validation và normalization
 * - Tích hợp với StudentAnswerItem
 */
object QuestionHandler {

    /**
     * Đánh giá đáp án cho câu hỏi single_choice (trắc nghiệm)
     * Format đáp án: answer_id (Int)
     * Chỉ có 1 đáp án đúng, người dùng chọn 1 đáp án
     */
    fun evaluateSingleChoice(question: Question, userAnswerText: String): QuestionResult {
        return try {
            val selectedAnswerId = userAnswerText.toIntOrNull()
            val selectedAnswer = question.answers.find { it.answers_id == selectedAnswerId }
            val correctAnswer = question.answers.find { it.is_correct == 1 }

            QuestionResult(
                questionId = question.questions_id,
                questionType = question.question_type,
                isCorrect = selectedAnswer?.is_correct == 1,
                selectedAnswer = selectedAnswer?.answer_text ?: userAnswerText,
                correctAnswer = correctAnswer?.answer_text ?: "",
                score = if (selectedAnswer?.is_correct == 1) 1.0 else 0.0,
                explanation = "Đáp án đúng: ${correctAnswer?.answer_text}",
                normalizedAnswer = selectedAnswerId?.toString() ?: userAnswerText
            )
        } catch (e: Exception) {
            QuestionResult(
                questionId = question.questions_id,
                questionType = question.question_type,
                isCorrect = false,
                selectedAnswer = userAnswerText,
                correctAnswer = "",
                score = 0.0,
                explanation = "Lỗi định dạng đáp án",
                normalizedAnswer = userAnswerText
            )
        }
    }

    /**
     * Chuẩn hóa đáp án single_choice
     * Chuyển từ answer_text sang answer_id
     */
    fun normalizeSingleChoiceAnswer(question: Question, answerText: String): String {
        // Nếu đã là answer_id thì giữ nguyên
        answerText.toIntOrNull()?.let { return it.toString() }

        // Tìm answer_id từ answer_text
        val answer = question.answers.find { it.answer_text.equals(answerText.trim(), ignoreCase = true) }
        return answer?.answers_id?.toString() ?: answerText
    }

    /**
     * Đánh giá đáp án cho câu hỏi matching (nối từ)
     * Format đáp án: "left_id:right_id,left_id:right_id"
     * Theo API: có nhiều cặp đúng (is_correct = 1)
     */
    fun evaluateMatching(question: Question, userAnswerText: String): QuestionResult {
        return try {
            // Nếu user chưa trả lời gì (empty hoặc blank), trả về 0 điểm
            if (userAnswerText.isBlank()) {
                return QuestionResult(
                    questionId = question.questions_id,
                    questionType = question.question_type,
                    isCorrect = false,
                    selectedAnswer = "Chưa trả lời",
                    correctAnswer = formatCorrectMatchingDisplay(question.answers.filter { it.is_correct == 1 }),
                    score = 0.0,
                    explanation = "Chưa nối từ nào",
                    normalizedAnswer = ""
                )
            }

            val userMatches = parseMatchingAnswer(userAnswerText)
            val correctAnswers = question.answers.filter { it.is_correct == 1 }

            // Nếu user chưa nối cặp nào, trả về 0 điểm
            if (userMatches.isEmpty()) {
                return QuestionResult(
                    questionId = question.questions_id,
                    questionType = question.question_type,
                    isCorrect = false,
                    selectedAnswer = "Chưa trả lời",
                    correctAnswer = formatCorrectMatchingDisplay(correctAnswers),
                    score = 0.0,
                    explanation = "Chưa nối từ nào",
                    normalizedAnswer = ""
                )
            }

            var correctCount = 0

            // Tạo map từ match_key đến answer_text cho các đáp án đúng
            val correctPairs = correctAnswers.associate { it.match_key to it.answer_text }

            userMatches.forEach { (leftId, rightId) ->
                val leftAnswer = question.answers.find { it.answers_id == leftId.toIntOrNull() }
                val rightAnswer = question.answers.find { it.answers_id == rightId.toIntOrNull() }

                if (leftAnswer != null && rightAnswer != null) {
                    val expectedRightText = correctPairs[leftAnswer.match_key]
                    if (expectedRightText == rightAnswer.answer_text) {
                        correctCount++
                    }
                }
            }

            val score = if (correctAnswers.isNotEmpty()) {
                correctCount.toDouble() / correctAnswers.size.toDouble()
            } else 0.0

            QuestionResult(
                questionId = question.questions_id,
                questionType = question.question_type,
                isCorrect = score >= 1.0, // Phải nối đúng tất cả cặp đúng
                selectedAnswer = formatMatchingDisplay(question, userMatches),
                correctAnswer = formatCorrectMatchingDisplay(correctAnswers),
                score = score,
                explanation = "Nối đúng $correctCount/${correctAnswers.size} cặp",
                normalizedAnswer = userAnswerText
            )
        } catch (e: Exception) {
            QuestionResult(
                questionId = question.questions_id,
                questionType = question.question_type,
                isCorrect = false,
                selectedAnswer = userAnswerText,
                correctAnswer = "",
                score = 0.0,
                explanation = "Lỗi định dạng đáp án matching",
                normalizedAnswer = userAnswerText
            )
        }
    }

    /**
     * Parse matching answer từ format "left_id:right_id,left_id:right_id"
     */
    private fun parseMatchingAnswer(answerText: String): Map<String, String> {
        if (answerText.isBlank()) return emptyMap()

        return answerText.split(",").mapNotNull { pair ->
            val parts = pair.split(":")
            if (parts.size == 2) {
                parts[0].trim() to parts[1].trim()
            } else null
        }.toMap()
    }

    /**
     * Format matching display cho user
     */
    private fun formatMatchingDisplay(question: Question, userMatches: Map<String, String>): String {
        return userMatches.map { (leftId, rightId) ->
            val leftText = question.answers.find { it.answers_id == leftId.toIntOrNull() }?.answer_text ?: leftId
            val rightText = question.answers.find { it.answers_id == rightId.toIntOrNull() }?.answer_text ?: rightId
            "$leftText → $rightText"
        }.joinToString(", ")
    }

    /**
     * Format correct matching display
     */
    private fun formatCorrectMatchingDisplay(correctAnswers: List<Answer>): String {
        val correctPairs = correctAnswers.associate { it.match_key to it.answer_text }
        return correctPairs.map { (key, value) -> "$key → $value" }.joinToString(", ")
    }

    /**
     * Đánh giá đáp án cho câu hỏi classification (phân loại)
     * Format đáp án: "word_id:category,word_id:category"
     * Phân loại từ vào các nhóm dựa trên match_key
     */
    fun evaluateClassification(question: Question, userAnswerText: String): QuestionResult {
        return try {
            val userClassifications = parseClassificationAnswer(userAnswerText)
            val allWords = question.answers
            var correctCount = 0
            var totalWords = 0

            allWords.forEach { word ->
                val userCategory = userClassifications[word.answers_id.toString()]
                val correctCategory = if (word.is_correct == 1) word.match_key else null

                if (correctCategory != null) {
                    totalWords++
                    if (userCategory == correctCategory) {
                        correctCount++
                    }
                }
            }

            val score = if (totalWords > 0) {
                correctCount.toDouble() / totalWords.toDouble()
            } else 0.0

            QuestionResult(
                questionId = question.questions_id,
                questionType = question.question_type,
                isCorrect = score >= 1.0, // Phải phân loại đúng tất cả từ
                selectedAnswer = formatClassificationDisplay(question, userClassifications),
                correctAnswer = formatCorrectClassificationDisplay(question),
                score = score,
                explanation = "Phân loại đúng $correctCount/$totalWords từ",
                normalizedAnswer = userAnswerText
            )
        } catch (e: Exception) {
            QuestionResult(
                questionId = question.questions_id,
                questionType = question.question_type,
                isCorrect = false,
                selectedAnswer = userAnswerText,
                correctAnswer = "",
                score = 0.0,
                explanation = "Lỗi định dạng đáp án classification",
                normalizedAnswer = userAnswerText
            )
        }
    }

    /**
     * Parse classification answer từ format "word_id:category,word_id:category"
     */
    private fun parseClassificationAnswer(answerText: String): Map<String, String> {
        if (answerText.isBlank()) return emptyMap()

        return answerText.split(",").mapNotNull { pair ->
            val parts = pair.split(":")
            if (parts.size == 2) {
                parts[0].trim() to parts[1].trim()
            } else null
        }.toMap()
    }

    /**
     * Format classification display cho user
     */
    private fun formatClassificationDisplay(question: Question, userClassifications: Map<String, String>): String {
        return userClassifications.map { (wordId, category) ->
            val wordText = question.answers.find { it.answers_id == wordId.toIntOrNull() }?.answer_text ?: wordId
            "$wordText → $category"
        }.joinToString(", ")
    }

    /**
     * Format correct classification display
     */
    private fun formatCorrectClassificationDisplay(question: Question): String {
        return question.answers.filter { it.is_correct == 1 }.map { word ->
            "${word.answer_text} → ${word.match_key}"
        }.joinToString(", ")
    }

    /**
     * Đánh giá đáp án cho câu hỏi fill_blank (điền từ)
     * Format đáp án: text (normalized)
     * Chỉ có 1 hoặc nhiều đáp án đúng
     */
    fun evaluateFillBlank(question: Question, userAnswerText: String): QuestionResult {
        val normalizedUserAnswer = normalizeFillBlankAnswer(userAnswerText)
        val correctAnswers = question.answers.filter { it.is_correct == 1 }

        val isCorrect = correctAnswers.any {
            normalizeFillBlankAnswer(it.answer_text) == normalizedUserAnswer
        }

        return QuestionResult(
            questionId = question.questions_id,
            questionType = question.question_type,
            isCorrect = isCorrect,
            selectedAnswer = userAnswerText,
            correctAnswer = correctAnswers.joinToString(" / ") { it.answer_text },
            score = if (isCorrect) 1.0 else 0.0,
            explanation = "Đáp án đúng: ${correctAnswers.joinToString(" / ") { it.answer_text }}",
            normalizedAnswer = normalizedUserAnswer
        )
    }

    /**
     * Chuẩn hóa đáp án fill_blank
     */
    private fun normalizeFillBlankAnswer(answer: String): String {
        return answer.trim().lowercase()
            .replace(Regex("\\s+"), " ") // Thay thế nhiều khoảng trắng bằng 1
            .replace(Regex("[^a-zA-Z0-9\\s]"), "") // Loại bỏ ký tự đặc biệt
    }

    /**
     * Đánh giá đáp án cho câu hỏi arrangement (sắp xếp từ thành câu)
     * Format đáp án: "answer_id,answer_id,answer_id" theo thứ tự
     * Sắp xếp các từ theo order_index
     */
    fun evaluateArrangement(question: Question, userAnswerText: String): QuestionResult {
        return try {
            val userOrderIds = parseArrangementAnswer(userAnswerText)
            val correctOrder = question.answers
                .filter { it.is_correct == 1 }
                .sortedBy { it.order_index }
                .map { it.answers_id }

            val isCorrect = userOrderIds == correctOrder

            val userOrderTexts = userOrderIds.mapNotNull { id ->
                question.answers.find { it.answers_id == id }?.answer_text
            }

            val correctOrderTexts = correctOrder.mapNotNull { id ->
                question.answers.find { it.answers_id == id }?.answer_text
            }

            QuestionResult(
                questionId = question.questions_id,
                questionType = question.question_type,
                isCorrect = isCorrect,
                selectedAnswer = userOrderTexts.joinToString(" "),
                correctAnswer = correctOrderTexts.joinToString(" "),
                score = if (isCorrect) 1.0 else 0.0,
                explanation = "Thứ tự đúng: ${correctOrderTexts.joinToString(" ")}",
                normalizedAnswer = userAnswerText
            )
        } catch (e: Exception) {
            QuestionResult(
                questionId = question.questions_id,
                questionType = question.question_type,
                isCorrect = false,
                selectedAnswer = userAnswerText,
                correctAnswer = "",
                score = 0.0,
                explanation = "Lỗi định dạng đáp án arrangement",
                normalizedAnswer = userAnswerText
            )
        }
    }

    /**
     * Parse arrangement answer từ format "answer_id,answer_id,answer_id"
     */
    private fun parseArrangementAnswer(answerText: String): List<Int> {
        if (answerText.isBlank()) return emptyList()

        return answerText.split(",").mapNotNull { it.trim().toIntOrNull() }
    }

    /**
     * Đánh giá đáp án cho câu hỏi image_word (nhìn ảnh sắp xếp từ)
     * Format đáp án: text (normalized)
     * Sắp xếp các chữ cái thành từ đúng
     */
    fun evaluateImageWord(question: Question, userAnswerText: String): QuestionResult {
        val normalizedUserWord = normalizeImageWordAnswer(userAnswerText)
        val correctLetters = question.answers
            .filter { it.is_correct == 1 }
            .sortedBy { it.order_index }
            .map { it.answer_text }
        val correctWord = correctLetters.joinToString("")
        val normalizedCorrectWord = normalizeImageWordAnswer(correctWord)

        val isCorrect = normalizedUserWord == normalizedCorrectWord

        return QuestionResult(
            questionId = question.questions_id,
            questionType = question.question_type,
            isCorrect = isCorrect,
            selectedAnswer = userAnswerText,
            correctAnswer = correctWord,
            score = if (isCorrect) 1.0 else 0.0,
            explanation = "Từ đúng: $correctWord",
            normalizedAnswer = normalizedUserWord
        )
    }

    /**
     * Chuẩn hóa đáp án image_word
     */
    private fun normalizeImageWordAnswer(answer: String): String {
        return answer.trim().lowercase()
            .replace(Regex("[^a-zA-Z]"), "") // Chỉ giữ lại chữ cái
    }

    /**
     * Đánh giá đáp án dựa trên loại câu hỏi
     */
    fun evaluateAnswer(question: Question, userAnswerText: String): QuestionResult {
        return when (question.question_type) {
            "single_choice" -> evaluateSingleChoice(question, userAnswerText)
            "matching" -> evaluateMatching(question, userAnswerText)
            "classification" -> evaluateClassification(question, userAnswerText)
            "fill_blank" -> evaluateFillBlank(question, userAnswerText)
            "arrangement" -> evaluateArrangement(question, userAnswerText)
            "image_word" -> evaluateImageWord(question, userAnswerText)
            else -> QuestionResult(
                questionId = question.questions_id,
                questionType = question.question_type,
                isCorrect = false,
                selectedAnswer = userAnswerText,
                correctAnswer = "",
                score = 0.0,
                explanation = "Loại câu hỏi không được hỗ trợ: ${question.question_type}",
                normalizedAnswer = userAnswerText
            )
        }
    }

    /**
     * Chuẩn hóa đáp án dựa trên loại câu hỏi
     */
    fun normalizeAnswer(question: Question, answerText: String): String {
        return when (question.question_type) {
            "single_choice" -> normalizeSingleChoiceAnswer(question, answerText)
            "fill_blank" -> normalizeFillBlankAnswer(answerText)
            "image_word" -> normalizeImageWordAnswer(answerText)
            else -> answerText // Các loại khác giữ nguyên format
        }
    }

    /**
     * Tính điểm tổng cho một bài làm (0-100%)
     */
    fun calculateTotalScore(results: List<QuestionResult>): Double {
        if (results.isEmpty()) return 0.0
        val totalScore = results.sumOf { it.score }
        return (totalScore / results.size) * 100
    }

    /**
     * Kiểm tra xem bài làm có đạt yêu cầu không (>= 70%)
     */
    fun isPassed(results: List<QuestionResult>): Boolean {
        return calculateTotalScore(results) >= 70.0
    }

    /**
     * Tạo submission data để gửi lên API với đánh giá đúng/sai
     */
    fun createSubmissionData(results: List<QuestionResult>): List<StudentAnswerItem> {
        return results.map { result ->
            StudentAnswerItem(
                question_id = result.questionId,
                answer_text = result.normalizedAnswer,
                is_correct = result.isCorrect
            )
        }
    }

    /**
     * Đánh giá tất cả đáp án trong một lesson part
     */
    fun evaluateAllAnswers(
        questions: List<Question>,
        userAnswers: Map<Int, String>
    ): List<QuestionResult> {
        return questions.map { question ->
            val userAnswer = userAnswers[question.questions_id] ?: ""
            evaluateAnswer(question, userAnswer)
        }
    }
}

/**
 * Data class chứa kết quả đánh giá một câu hỏi
 */
data class QuestionResult(
    val questionId: Int,
    val questionType: String,
    val isCorrect: Boolean,
    val selectedAnswer: String, // Đáp án hiển thị cho user
    val correctAnswer: String, // Đáp án đúng hiển thị cho user
    val score: Double, // 0.0 - 1.0
    val explanation: String,
    val normalizedAnswer: String // Đáp án đã chuẩn hóa để gửi lên server
)

/**
 * Data class chứa kết quả đánh giá toàn bộ bài làm
 */
data class ExerciseResult(
    val lessonPartId: Int,
    val studentId: Int,
    val courseId: Int,
    val questionResults: List<QuestionResult>,
    val totalScore: Double,
    val isPassed: Boolean,
    val submissionTime: String
)
