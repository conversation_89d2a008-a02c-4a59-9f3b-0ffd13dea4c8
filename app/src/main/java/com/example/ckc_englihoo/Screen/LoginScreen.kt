package com.example.ckc_englihoo.Screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.VisibilityOff
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.R

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LoginScreen(
    navController: NavController,
    viewModel: AppViewModel = viewModel()
) {
    // States
    var username by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var isStudent by remember { mutableStateOf(true) }
    var passwordVisible by remember { mutableStateOf(false) }
    var usernameError by remember { mutableStateOf<String?>(null) }
    var passwordError by remember { mutableStateOf<String?>(null) }
    var showSuccessMessage by remember { mutableStateOf(false) }

    // ViewModel states
    val isLoading by viewModel.isLoading.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()
    val isLoggedIn by viewModel.isLoggedIn.collectAsState()

    // Navigate when login successful
    LaunchedEffect(isLoggedIn) {
        if (isLoggedIn) {
            // Show success message briefly
            showSuccessMessage = true

            // Clear form data on successful login
            username = ""
            password = ""
            usernameError = null
            passwordError = null

            // Navigate after a short delay to show success message
            kotlinx.coroutines.delay(1500)
            navController.navigate("main") {
                popUpTo("login") { inclusive = true }
            }
        }
    }

    // Clear errors when user changes role
    LaunchedEffect(isStudent) {
        usernameError = null
        passwordError = null
        viewModel.clearErrorMessage()
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // Nội dung chính
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(55.dp))

            // Logo ở đầu với hiệu ứng shadow
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 24.dp),
                contentAlignment = Alignment.Center
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(horizontal = 8.dp)
                ) {
                    // Logo ở bên trái
                    Image(
                        painter = painterResource(id = R.drawable.logocaothang),
                        contentDescription = "Logo",
                        modifier = Modifier
                            .size(48.dp)
                            .padding(end = 12.dp)
                    )

                    // Cột chứa 2 dòng tiêu đề bên phải
                    Column {
                        // Tiêu đề dòng 1
                        Text(
                            text = "TRƯỜNG CĐ KỸ THUẬT CAO THẮNG",
                            color = Color(0xFF0066CC),
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Bold
                        )

                        // Tiêu đề dòng 2
                        Text(
                            text = "TRUNG TÂM NGOẠI NGỮ",
                            color = Color(0xFFFF0000),
                            fontSize = 20.sp,
                            fontWeight = FontWeight.ExtraBold,
                            letterSpacing = 0.5.sp
                        )
                    }
                }
            }
            Spacer(modifier = Modifier.height(15.dp))

            // Card chứa form đăng nhập
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .border(
                        width = 1.dp,
                        color = Color(0xFFBBDEFB), // Màu xám trung tính nhẹ
                        shape = RoundedCornerShape(16.dp)
                    ),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color(0xFFF0F0F0) // Màu xám nhạt
                ),
                shape = RoundedCornerShape(16.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(24.dp)
                ) {
                    // Tiêu đề Login
                    Text(
                        text = if (isStudent) "Đăng nhập Học sinh" else "Đăng nhập Giáo viên",
                        style = MaterialTheme.typography.headlineMedium,
                        fontWeight = FontWeight.Bold,
                        color = Color.Black,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )

                    Text(
                        text = "Đăng nhập bằng tên người dùng và mật khẩu được cung cấp.",
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color.Gray,
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // User type selection
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(4.dp),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        FilterChip(
                            onClick = { isStudent = true },
                            label = { Text("Học sinh") },
                            selected = isStudent,
                            modifier = Modifier.weight(1f),
                            colors = FilterChipDefaults.filterChipColors(
                                selectedContainerColor = Color(0xFF4355EE),
                                selectedLabelColor = Color.White
                            )
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        FilterChip(
                            onClick = { isStudent = false },
                            label = { Text("Giáo viên") },
                            selected = !isStudent,
                            modifier = Modifier.weight(1f),
                            colors = FilterChipDefaults.filterChipColors(
                                selectedContainerColor = Color(0xFF4355EE),
                                selectedLabelColor = Color.White
                            )
                        )
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    // Label cho field tài khoản
                    Text(
                        text = "Tên Đăng Nhập",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium,
                        color = Color.DarkGray
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // Trường nhập tài khoản với icon
                    OutlinedTextField(
                        value = username,
                        onValueChange = {
                            username = it
                            usernameError = null // Clear validation error when user types
                        },
                        placeholder = { Text("Nhập tên đăng nhập") },
                        singleLine = true,
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(8.dp),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = if (usernameError != null) Color.Red else Color(0xFF4355EE),
                            unfocusedBorderColor = if (usernameError != null) Color.Red else Color.LightGray
                        ),
                        keyboardOptions = KeyboardOptions(
                            keyboardType = KeyboardType.Text,
                            imeAction = ImeAction.Next
                        ),
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Person,
                                contentDescription = "Username Icon",
                                tint = Color(0xFF4355EE)
                            )
                        },
                        isError = usernameError != null
                    )

                    // Username error message
                    usernameError?.let { error ->
                        Text(
                            text = error,
                            color = Color.Red,
                            style = MaterialTheme.typography.bodySmall,
                            modifier = Modifier.padding(start = 16.dp, top = 4.dp)
                        )
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Label cho field mật khẩu
                    Text(
                        text = "Mật Khẩu",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium,
                        color = Color.DarkGray
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // Trường nhập mật khẩu với icon
                    OutlinedTextField(
                        value = password,
                        onValueChange = {
                            password = it
                            passwordError = null // Clear validation error when user types
                        },
                        placeholder = { Text("Nhập mật khẩu") },
                        singleLine = true,
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(8.dp),
                        visualTransformation = if (passwordVisible) VisualTransformation.None else PasswordVisualTransformation(),
                        keyboardOptions = KeyboardOptions(
                            keyboardType = KeyboardType.Password,
                            imeAction = ImeAction.Done
                        ),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = if (passwordError != null) Color.Red else Color(0xFF4355EE),
                            unfocusedBorderColor = if (passwordError != null) Color.Red else Color.LightGray
                        ),
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Lock,
                                contentDescription = "Password Icon",
                                tint = Color(0xFF4355EE)
                            )
                        },
                        trailingIcon = {
                            IconButton(onClick = { passwordVisible = !passwordVisible }) {
                                Icon(
                                    imageVector = if (passwordVisible) Icons.Filled.Visibility else Icons.Filled.VisibilityOff,
                                    contentDescription = if (passwordVisible) "Ẩn mật khẩu" else "Hiện mật khẩu",
                                    tint = Color.Gray
                                )
                            }
                        },
                        isError = passwordError != null
                    )

                    // Password error message
                    passwordError?.let { error ->
                        Text(
                            text = error,
                            color = Color.Red,
                            style = MaterialTheme.typography.bodySmall,
                            modifier = Modifier.padding(start = 16.dp, top = 4.dp)
                        )
                    }

                    // Text quên mật khẩu với icon
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        contentAlignment = Alignment.CenterEnd
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.clickable { /* Handle forgot password */ }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Info,
                                contentDescription = "Quên mật khẩu Icon",
                                tint = Color.Red,
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(
                                text = "Quên mật khẩu vui lòng liên hệ giáo viên?",
                                color = Color.Red,
                                fontSize = 13.sp
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    // API error message với icon và styling tốt hơn
                    errorMessage?.let { error ->
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            colors = CardDefaults.cardColors(
                                containerColor = Color(0xFFFFEBEE) // Light red background
                            ),
                            shape = RoundedCornerShape(8.dp),
                            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                        ) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(16.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Info,
                                    contentDescription = "Error",
                                    tint = Color(0xFFD32F2F), // Red color
                                    modifier = Modifier.size(20.dp)
                                )
                                Spacer(modifier = Modifier.width(12.dp))
                                Text(
                                    text = error,
                                    color = Color(0xFFD32F2F),
                                    style = MaterialTheme.typography.bodyMedium,
                                    fontWeight = FontWeight.Medium
                                )
                            }
                        }
                        Spacer(modifier = Modifier.height(16.dp))
                    }

                    // Nút đăng nhập với hiệu ứng shadow
                    Button(
                        onClick = {
                            // Clear previous errors
                            viewModel.clearErrorMessage()
                            usernameError = null
                            passwordError = null

                            // Enhanced validation
                            var hasError = false

                            // Username validation
                            when {
                                username.isBlank() -> {
                                    usernameError = "Vui lòng nhập tên đăng nhập"
                                    hasError = true
                                }
                                username.length < 3 -> {
                                    usernameError = "Tên đăng nhập phải có ít nhất 3 ký tự"
                                    hasError = true
                                }
                                !username.matches(Regex("^[a-zA-Z0-9._-]+$")) -> {
                                    usernameError = "Tên đăng nhập chỉ được chứa chữ, số và ký tự ._-"
                                    hasError = true
                                }
                            }

                            // Password validation
                            when {
                                password.isBlank() -> {
                                    passwordError = "Vui lòng nhập mật khẩu"
                                    hasError = true
                                }
                                password.length < 6 -> {
                                    passwordError = "Mật khẩu phải có ít nhất 6 ký tự"
                                    hasError = true
                                }
                                password.length > 50 -> {
                                    passwordError = "Mật khẩu không được quá 50 ký tự"
                                    hasError = true
                                }
                            }

                            // Only proceed if no validation errors
                            if (!hasError) {
                                if (isStudent) {
                                    viewModel.loginStudent(username.trim(), password)
                                } else {
                                    viewModel.loginTeacher(username.trim(), password)
                                }
                            }
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(50.dp),
                        shape = RoundedCornerShape(8.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF4355EE)
                        ),
                        elevation = ButtonDefaults.buttonElevation(
                            defaultElevation = 6.dp,
                            pressedElevation = 8.dp
                        ),
                        enabled = !isLoading
                    ) {
                        if (isLoading) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(20.dp),
                                color = Color.White,
                                strokeWidth = 2.dp
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "Đang đăng nhập...",
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold,
                                color = Color.White
                            )
                        } else {
                            Text(
                                text = "Đăng nhập",
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold,
                                color = Color.White
                            )
                        }
                    }
                }
            }
        }

        // Success message overlay
        if (showSuccessMessage) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                contentAlignment = Alignment.Center
            ) {
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFFE8F5E8) // Light green background
                    ),
                    shape = RoundedCornerShape(12.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
                ) {
                    Row(
                        modifier = Modifier.padding(20.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Info,
                            contentDescription = "Success",
                            tint = Color(0xFF4CAF50), // Green color
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Text(
                            text = "Đăng nhập thành công!",
                            color = Color(0xFF2E7D32),
                            style = MaterialTheme.typography.bodyLarge,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
        }
    }
}
