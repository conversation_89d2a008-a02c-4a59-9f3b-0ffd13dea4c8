package com.example.ckc_englihoo.Screen

import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalUriHandler
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.DataClass.*
import com.example.ckc_englihoo.R
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MyProfileTeacher(
    navController: NavController,
    viewModel: AppViewModel
) {
    val currentTeacher by viewModel.currentTeacher.collectAsState()
    val primaryColor = Color(0xFF1976D2) // Blue color for teacher theme

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // Spacer để cân bằng với nút back (48dp)
                        Spacer(modifier = Modifier.width(48.dp))

                        // Title căn giữa
                        Text(
                            "Hồ Sơ Giảng Viên",
                            color = Color.White,
                            fontWeight = FontWeight.Bold,
                            fontSize = 20.sp,
                            letterSpacing = 0.5.sp,
                            modifier = Modifier.weight(1f),
                            textAlign = TextAlign.Center
                        )

                        // Spacer để cân bằng bên phải (48dp - tương đương với icon)
                        Spacer(modifier = Modifier.width(48.dp))
                    }
                },
                navigationIcon = {
                    IconButton(
                        onClick = { navController.popBackStack() },
                        modifier = Modifier
                            .size(48.dp)
                            .clip(CircleShape)
                    ) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Quay lại",
                            tint = Color.White,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = primaryColor
                )
            )
        }
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = androidx.compose.ui.graphics.Brush.verticalGradient(
                        colors = listOf(
                            Color(0xFFE3F2FD), // Light blue background
                            Color(0xFFFFFFFF)
                        )
                    )
                )
        ) {
            currentTeacher?.let { teacher ->
                TeacherProfileContent(
                    teacher = teacher,
                    viewModel = viewModel,
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(innerPadding)
                        .verticalScroll(rememberScrollState())
                        .padding(20.dp)
                )
            } ?: run {
                // Show loading or error state
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(innerPadding),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "Không thể tải thông tin giảng viên",
                        style = MaterialTheme.typography.bodyLarge,
                        color = Color.Gray
                    )
                }
            }
        }
    }
}

@Composable
fun TeacherProfileContent(
    teacher: Teacher,
    viewModel: AppViewModel,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Spacer(modifier = Modifier.height(10.dp))

        // Profile Header Card
        TeacherProfileHeaderCard(teacher)

        Spacer(modifier = Modifier.height(20.dp))

        // Personal Information Card
        TeacherPersonalInfoCard(teacher)

        Spacer(modifier = Modifier.height(20.dp))

        // Professional Information Card
        TeacherProfessionalInfoCard(teacher)

        Spacer(modifier = Modifier.height(20.dp))

        // Contact & External Links Card
        TeacherContactCard()

        Spacer(modifier = Modifier.height(20.dp))
    }
}

@Composable
fun TeacherProfileHeaderCard(teacher: Teacher) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 8.dp,
            hoveredElevation = 12.dp
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Avatar
            Image(
                painter = painterResource(R.drawable.teacher),
                contentDescription = "Avatar",
                modifier = Modifier
                    .size(100.dp)
                    .clip(CircleShape)
                    .border(4.dp, Color(0xFF1976D2), CircleShape) // Blue border for teacher
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Teacher Name
            Text(
                text = teacher.fullname,
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF111827),
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(8.dp))

            // Teacher ID Badge
            Surface(
                color = Color(0xFF1976D2), // Blue color for teacher
                shape = RoundedCornerShape(20.dp)
            ) {
                Text(
                    text = "Mã GV: ${teacher.email.substringBefore("@")}",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.White,
                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                )
            }
        }
    }
}

@Composable
fun TeacherPersonalInfoCard(teacher: Teacher) {
    TeacherInfoCard(
        title = "Thông Tin Cá Nhân",
        icon = Icons.Filled.Person
    ) {
        TeacherProfileInfoRow(
            label = "Họ và Tên Đầy Đủ",
            value = teacher.fullname,
            icon = Icons.Filled.Person
        )

        TeacherProfileInfoRow(
            label = "Địa Chỉ Email",
            value = teacher.email,
            icon = Icons.Filled.Email
        )

        TeacherProfileInfoRow(
            label = "Ngày Sinh",
            value = formatTeacherDate(teacher.date_of_birth),
            icon = Icons.Filled.DateRange
        )

        TeacherProfileInfoRow(
            label = "Giới Tính",
            value = if (teacher.gender == 1) "Nam" else "Nữ",
            icon = Icons.Filled.Person
        )

        TeacherProfileInfoRow(
            label = "Tên Đăng Nhập",
            value = teacher.username,
            icon = Icons.Filled.AccountCircle
        )
    }
}

@Composable
fun TeacherProfessionalInfoCard(teacher: Teacher) {
    TeacherInfoCard(
        title = "Thông Tin Nghề Nghiệp",
        icon = Icons.Filled.Work
    ) {
        TeacherProfileInfoRow(
            label = "Chuyên Môn",
            value = "Tiếng Anh", // Default specialization
            icon = Icons.Filled.School
        )

        TeacherProfileInfoRow(
            label = "Vai Trò",
            value = "Giảng Viên",
            icon = Icons.Filled.Timeline
        )

        TeacherProfileInfoRow(
            label = "Trạng Thái Tài Khoản",
            value = if (teacher.is_status == 1) "Đang Hoạt Động" else "Tạm Khóa",
            icon = Icons.Filled.Info,
            valueColor = if (teacher.is_status == 1) Color(0xFF10B981) else Color(0xFFE53E3E)
        )

        TeacherProfileInfoRow(
            label = "Ngày Tạo Tài Khoản",
            value = formatTeacherDate(teacher.created_at),
            icon = Icons.Filled.CalendarToday
        )

        TeacherProfileInfoRow(
            label = "Cập Nhật Lần Cuối",
            value = formatTeacherDate(teacher.updated_at),
            icon = Icons.Filled.Update
        )
    }
}

@Composable
fun TeacherContactCard() {
    TeacherInfoCard(
        title = "Liên Hệ & Hỗ Trợ",
        icon = Icons.Filled.ContactSupport
    ) {
        TeacherExternalLinkRow(
            icon = R.drawable.facebook,
            text = "Trung Tâm Ngoại Ngữ Trường Cao Thắng",
            uri = "https://www.facebook.com/englishcenter.caothang.edu.vn"
        )

        TeacherProfileInfoRow(
            label = "Hotline Hỗ Trợ",
            value = "028 3950 1234",
            icon = Icons.Filled.Phone
        )

        TeacherProfileInfoRow(
            label = "Email Hỗ Trợ",
            value = "<EMAIL>",
            icon = Icons.Filled.Email
        )

        TeacherProfileInfoRow(
            label = "Địa Chỉ Trường",
            value = "65 Huỳnh Thúc Kháng, Quận 1, TP.HCM",
            icon = Icons.Filled.LocationOn
        )
    }
}

@Composable
fun TeacherInfoCard(
    title: String,
    icon: ImageVector,
    content: @Composable ColumnScope.() -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 8.dp,
            hoveredElevation = 12.dp
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(modifier = Modifier.fillMaxWidth()) {
            // Header với gradient background
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        brush = androidx.compose.ui.graphics.Brush.horizontalGradient(
                            colors = listOf(
                                Color(0xFF1976D2), // Blue for teacher
                                Color(0xFF42A5F5)  // Lighter blue
                            )
                        ),
                        shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
                    )
                    .padding(20.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(
                        imageVector = icon,
                        contentDescription = title,
                        tint = Color.White,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = title,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                }
            }

            // Content với padding đẹp
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
            ) {
                content()
            }
        }
    }
}
@Composable
fun TeacherProfileInfoRow(
    label: String,
    value: String,
    icon: ImageVector,
    valueColor: Color = Color(0xFF111827)
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 3.dp),
        shape = RoundedCornerShape(12.dp),
        border = androidx.compose.foundation.BorderStroke(
            width = 1.dp,
            color = Color(0xFFE5E7EB)
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    brush = androidx.compose.ui.graphics.Brush.horizontalGradient(
                        colors = listOf(
                            Color(0xFFFAFBFC),
                            Color.White
                        )
                    )
                )
                .padding(16.dp)
        ) {
            // Label row with icon
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                // Icon
                Icon(
                    imageVector = icon,
                    contentDescription = label,
                    tint = Color(0xFF1976D2), // Blue for teacher
                    modifier = Modifier.size(20.dp)
                )

                Spacer(modifier = Modifier.width(12.dp))

                // Label
                Text(
                    text = label,
                    fontSize = 15.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF374151)
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Value - can wrap to multiple lines
            Text(
                text = value,
                fontSize = 15.sp,
                fontWeight = FontWeight.Medium,
                color = valueColor,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 32.dp), // Align with text after icon
                lineHeight = 20.sp
            )
        }
    }
}

@Composable
fun TeacherExternalLinkRow(icon: Int, text: String, uri: String) {
    val handler = LocalUriHandler.current

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
            .clickable { handler.openUri(uri) },
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 3.dp),
        shape = RoundedCornerShape(12.dp),
        border = androidx.compose.foundation.BorderStroke(
            width = 1.dp,
            color = Color(0xFFE5E7EB)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    brush = androidx.compose.ui.graphics.Brush.horizontalGradient(
                        colors = listOf(
                            Color(0xFFFAFBFC),
                            Color.White
                        )
                    )
                )
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(icon),
                contentDescription = null,
                modifier = Modifier.size(20.dp)
            )
            Spacer(modifier = Modifier.width(12.dp))
            Text(
                text = text,
                fontSize = 15.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF1976D2)
            )
        }
    }
}

// Helper function to format date for teacher
fun formatTeacherDate(dateString: String?): String {
    if (dateString.isNullOrEmpty()) return "Chưa có thông tin"

    return try {
        val inputFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault())
        val outputFormat = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault())
        val date = inputFormat.parse(dateString)
        outputFormat.format(date ?: Date())
    } catch (e: Exception) {
        try {
            val inputFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            val outputFormat = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault())
            val date = inputFormat.parse(dateString)
            outputFormat.format(date ?: Date())
        } catch (e: Exception) {
            dateString
        }
    }
}

@Preview(showBackground = true)
@Composable
fun TeacherProfileScreenPreview() {
    val sampleTeacher = Teacher(
        teacher_id = 1,
        fullname = "Nguyễn Văn Minh",
        username = "minh.teacher",
        email = "<EMAIL>",
        password = "123456",
        date_of_birth = "1985-03-15",
        gender = 1,
        is_status = 1,
        created_at = "2020-01-01T00:00:00.000Z",
        updated_at = "2024-12-19T00:00:00.000Z"
    )

    MaterialTheme {
        // Preview without ViewModel for simplicity
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(20.dp)
        ) {
            TeacherProfileHeaderCard(sampleTeacher)
            Spacer(modifier = Modifier.height(20.dp))
            TeacherPersonalInfoCard(sampleTeacher)
            Spacer(modifier = Modifier.height(20.dp))
            TeacherProfessionalInfoCard(sampleTeacher)
            Spacer(modifier = Modifier.height(20.dp))
            TeacherContactCard()
        }
    }
}
