package com.example.ckc_englihoo.Screen.Exercises

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.ckc_englihoo.DataClass.Question
import com.example.ckc_englihoo.Utils.AnswerFeedback

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FillBlankExerciseContent(
    question: Question,
    currentAnswer: String?, // Đ<PERSON><PERSON> án hiện tại từ ViewModel
    onAnswer: (String) -> Unit // Callback để gửi đáp án về ViewModel
) {
    // L<PERSON>y đáp án đúng từ câu hỏi.
    // Trong câu hỏi điền từ, is_correct = 1 thường đánh dấu đáp án cho blank đó.
    // Nếu có nhiều blank, bạn sẽ cần một logic phức tạp hơn để xử lý order_index.
    // Hiện tại, giả định một blank duy nhất hoặc blank đầu tiên.
    val correctAnswer = question.answers.firstOrNull { it.is_correct == 1 }?.answer_text

    // Sử dụng `currentAnswer` để khởi tạo `inputText`.
    // `remember(currentAnswer)` đảm bảo `inputText` được cập nhật khi `currentAnswer` thay đổi
    // (ví dụ: khi người dùng chuyển câu hỏi và quay lại).
    var inputText by remember(currentAnswer) { mutableStateOf(currentAnswer ?: "") }

    Column(
        modifier = Modifier
            .height(610.dp) // Có thể điều chỉnh chiều cao hoặc dùng fillMaxHeight()
            .fillMaxWidth()
            .background(Color(0xFFE0F7FA)) // Màu nền nhẹ
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // ✅ Card chứa nguyên câu hỏi
        Card(
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.padding(vertical = 24.dp, horizontal = 16.dp)
            ) {
                Text(
                    text = question.question_text,
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF0D47A1), // Màu chữ đậm
                    lineHeight = 28.sp,
                    textAlign = TextAlign.Center // Căn giữa văn bản câu hỏi
                )
            }
        }

        Spacer(modifier = Modifier.height(32.dp))

        // Ô nhập liệu cho đáp án
        OutlinedTextField(
            value = inputText,
            onValueChange = {
                inputText = it
                onAnswer(it) // Gửi đáp án đã thay đổi về ViewModel ngay lập tức
            },
            placeholder = { Text("Nhập câu trả lời...") },
            singleLine = true, // Chỉ cho phép một dòng
            modifier = Modifier
                .fillMaxWidth(0.8f) // Chiếm 80% chiều rộng
                .height(56.dp), // Chiều cao cố định
            shape = RoundedCornerShape(12.dp), // Bo tròn góc
            colors = TextFieldDefaults.outlinedTextFieldColors(
                containerColor = Color.White, // Màu nền của TextField
                focusedBorderColor = Color(0xFF2196F3), // Màu viền khi focus
                unfocusedBorderColor = Color.Gray // Màu viền khi không focus
            ),
            keyboardOptions = KeyboardOptions.Default.copy(imeAction = ImeAction.Done) // Bàn phím hiển thị nút "Done"
        )
        Spacer(modifier = Modifier.height(16.dp))

    }
}