package com.example.ckc_englihoo.Screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.clickable
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*

import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalUriHandler
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.tooling.preview.Preview
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.DataClass.Student
import com.example.ckc_englihoo.R
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StudentProfileScreen(
    navController: NavController,
    viewModel: AppViewModel
) {
    val currentStudent by viewModel.currentStudent.collectAsState()
    val primaryColor = Color(0xFF5BA3F5) // Xanh dương nhạt gợi nhớ

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // Spacer để cân bằng với nút back (48dp)
                        Spacer(modifier = Modifier.width(48.dp))

                        // Title căn giữa
                        Text(
                            "Hồ Sơ Sinh Viên",
                            color = Color.White,
                            fontWeight = FontWeight.Bold,
                            fontSize = 20.sp,
                            letterSpacing = 0.5.sp,
                            modifier = Modifier.weight(1f),
                            textAlign = TextAlign.Center
                        )

                        // Spacer để cân bằng bên phải (48dp - tương đương với icon)
                        Spacer(modifier = Modifier.width(48.dp))
                    }
                },
                navigationIcon = {
                    IconButton(
                        onClick = { navController.popBackStack() },
                        modifier = Modifier
                            .size(48.dp)
                            .clip(CircleShape)
                    ) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Quay lại",
                            tint = Color.White,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = primaryColor
                )
            )
        }
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = androidx.compose.ui.graphics.Brush.verticalGradient(
                        colors = listOf(
                            Color(0xFFF0F8FF), // Alice Blue - xanh dương nhạt gợi nhớ
                            Color(0xFFFFFFFF)
                        )
                    )
                )
        ) {
            currentStudent?.let { student ->
                StudentProfileContent(
                    student = student,
                    viewModel = viewModel,
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(innerPadding)
                        .verticalScroll(rememberScrollState())
                        .padding(20.dp)
                )
            } ?: run {
                // Show loading or error state
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(innerPadding),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "Không thể tải thông tin sinh viên",
                        style = MaterialTheme.typography.bodyLarge,
                        color = Color.Gray
                    )
                }
            }
        }
    }
}

@Composable
fun StudentProfileContent(
    student: Student,
    viewModel: AppViewModel,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Spacer(modifier = Modifier.height(10.dp))

        // Profile Header Card
        ProfileHeaderCard(student)

        Spacer(modifier = Modifier.height(20.dp))

        // Personal Information Card
        PersonalInfoCard(student)

        Spacer(modifier = Modifier.height(20.dp))

        // Academic Information Card
        AcademicInfoCard(student)

        Spacer(modifier = Modifier.height(20.dp))

        // Contact & Support Card
        StudentContactCard()

        Spacer(modifier = Modifier.height(20.dp))
    }
}

@Composable
fun ProfileHeaderCard(student: Student) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 8.dp,
            hoveredElevation = 12.dp
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Avatar
            Image(
                painter = painterResource(R.drawable.student),
                contentDescription = "Avatar",
                modifier = Modifier
                    .size(100.dp)
                    .clip(CircleShape)
                    .border(4.dp, Color(0xFF5BA3F5), CircleShape) // Xanh dương nhạt
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Student Name
            Text(
                text = student.fullname,
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF111827),
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(8.dp))

            // Student MSSV Badge
            Surface(
                color = Color(0xFF5BA3F5), // Xanh dương nhạt
                shape = RoundedCornerShape(20.dp)
            ) {
                Text(
                    text = "MSSV: ${student.email.substringBefore("@")}",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.White,
                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                )
            }
        }
    }
}

@Composable
fun PersonalInfoCard(student: Student) {
    InfoCard(
        title = "Thông Tin Cá Nhân",
        icon = Icons.Filled.Person
    ) {
        ProfileInfoRow(
            label = "Họ và Tên Đầy Đủ",
            value = student.fullname,
            icon = Icons.Filled.Person
        )

        ProfileInfoRow(
            label = "Địa Chỉ Email",
            value = student.email,
            icon = Icons.Filled.Email
        )

        ProfileInfoRow(
            label = "Ngày Sinh",
            value = formatDate(student.date_of_birth),
            icon = Icons.Filled.DateRange
        )

        ProfileInfoRow(
            label = "Giới Tính",
            value = if (student.gender == 1) "Nam" else "Nữ",
            icon = Icons.Filled.Person
        )

        ProfileInfoRow(
            label = "Tên Đăng Nhập",
            value = student.username,
            icon = Icons.Filled.AccountCircle
        )
    }
}

@Composable
fun AcademicInfoCard(student: Student) {
    InfoCard(
        title = "Thông Tin Học Tập",
        icon = Icons.Filled.School
    ) {
        ProfileInfoRow(
            label = "Trạng Thái Tài Khoản",
            value = if (student.is_status == 1) "Đang Hoạt Động" else "Tạm Khóa",
            icon = Icons.Filled.Info,
            valueColor = if (student.is_status == 1) Color(0xFF10B981) else Color(0xFFE53E3E)
        )

        ProfileInfoRow(
            label = "Ngày Tạo Tài Khoản",
            value = formatDate(student.created_at),
            icon = Icons.Filled.CalendarToday
        )

        ProfileInfoRow(
            label = "Cập Nhật Lần Cuối",
            value = formatDate(student.updated_at),
            icon = Icons.Filled.Update
        )
    }
}

@Composable
fun InfoCard(
    title: String,
    icon: ImageVector,
    content: @Composable ColumnScope.() -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 8.dp,
            hoveredElevation = 12.dp
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(modifier = Modifier.fillMaxWidth()) {
            // Header với gradient background
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        brush = androidx.compose.ui.graphics.Brush.horizontalGradient(
                            colors = listOf(
                                Color(0xFF5BA3F5), // Xanh dương nhạt
                                Color(0xFF87CEEB)  // Sky Blue nhạt hơn
                            )
                        ),
                        shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
                    )
                    .padding(20.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(
                        imageVector = icon,
                        contentDescription = title,
                        tint = Color.White,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = title,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                }
            }

            // Content với padding đẹp
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
            ) {
                content()
            }
        }
    }
}





@Composable
fun ProfileInfoRow(
    label: String,
    value: String,
    icon: ImageVector,
    valueColor: Color = Color(0xFF111827)
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 3.dp),
        shape = RoundedCornerShape(12.dp),
        border = androidx.compose.foundation.BorderStroke(
            width = 1.dp,
            color = Color(0xFFE5E7EB)
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    brush = androidx.compose.ui.graphics.Brush.horizontalGradient(
                        colors = listOf(
                            Color(0xFFFAFBFC),
                            Color.White
                        )
                    )
                )
                .padding(16.dp)
        ) {
            // Label row with icon
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                // Icon
                Icon(
                    imageVector = icon,
                    contentDescription = label,
                    tint = Color(0xFF5BA3F5), // Xanh dương nhạt
                    modifier = Modifier.size(20.dp)
                )

                Spacer(modifier = Modifier.width(12.dp))

                // Label
                Text(
                    text = label,
                    fontSize = 15.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF374151)
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Value - can wrap to multiple lines
            Text(
                text = value,
                fontSize = 15.sp,
                fontWeight = FontWeight.Medium,
                color = valueColor,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 32.dp), // Align with text after icon
                lineHeight = 20.sp
            )
        }
    }
}

@Composable
fun StudentContactCard() {
    InfoCard(
        title = "Liên Hệ & Hỗ Trợ",
        icon = Icons.Filled.ContactSupport
    ) {
        StudentExternalLinkRow(
            icon = R.drawable.facebook,
            text = "Trung Tâm Ngoại Ngữ Trường Cao Thắng",
            uri = "https://www.facebook.com/englishcenter.caothang.edu.vn"
        )

        ProfileInfoRow(
            label = "Hotline Hỗ Trợ",
            value = "028 3950 1234",
            icon = Icons.Filled.Phone
        )

        ProfileInfoRow(
            label = "Email Hỗ Trợ",
            value = "<EMAIL>",
            icon = Icons.Filled.Email
        )

        ProfileInfoRow(
            label = "Địa Chỉ Trường",
            value = "65 Huỳnh Thúc Kháng, Quận 1, TP.HCM",
            icon = Icons.Filled.LocationOn
        )
    }
}

@Composable
fun StudentExternalLinkRow(icon: Int, text: String, uri: String) {
    val handler = LocalUriHandler.current

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
            .clickable { handler.openUri(uri) },
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 3.dp),
        shape = RoundedCornerShape(12.dp),
        border = androidx.compose.foundation.BorderStroke(
            width = 1.dp,
            color = Color(0xFFE5E7EB)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    brush = androidx.compose.ui.graphics.Brush.horizontalGradient(
                        colors = listOf(
                            Color(0xFFFAFBFC),
                            Color.White
                        )
                    )
                )
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(icon),
                contentDescription = null,
                modifier = Modifier.size(20.dp)
            )
            Spacer(modifier = Modifier.width(12.dp))
            Text(
                text = text,
                fontSize = 15.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF5BA3F5) // Student theme color
            )
        }
    }
}

// Helper function to format date
fun formatDate(dateString: String?): String {
    if (dateString.isNullOrEmpty()) return "Chưa có thông tin"

    return try {
        val inputFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault())
        val outputFormat = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault())
        val date = inputFormat.parse(dateString)
        outputFormat.format(date ?: Date())
    } catch (e: Exception) {
        try {
            val inputFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            val outputFormat = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault())
            val date = inputFormat.parse(dateString)
            outputFormat.format(date ?: Date())
        } catch (e: Exception) {
            dateString
        }
    }
}

@Preview(showBackground = true)
@Composable
fun StudentProfileScreenPreview() {
    val sampleStudent = Student(
        student_id = 1,
        fullname = "Nguyễn Thị Huyền Trang",
        username = "trang",
        email = "<EMAIL>",
        password = "123456",
        date_of_birth = "2002-05-10",
        gender = 0,
        is_status = 1,
        created_at = "2024-01-01T00:00:00.000Z",
        updated_at = "2024-12-19T00:00:00.000Z"
    )

    MaterialTheme {
        // Preview without ViewModel for simplicity
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(20.dp)
        ) {
            ProfileHeaderCard(sampleStudent)
            Spacer(modifier = Modifier.height(20.dp))
            PersonalInfoCard(sampleStudent)
            Spacer(modifier = Modifier.height(20.dp))
            AcademicInfoCard(sampleStudent)
            Spacer(modifier = Modifier.height(20.dp))
            StudentContactCard()
        }
    }
}




