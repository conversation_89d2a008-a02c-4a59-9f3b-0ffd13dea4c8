package com.example.ckc_englihoo.Screen.Exercises

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Book
import androidx.compose.material.icons.filled.Category
import androidx.compose.material.icons.filled.ColorLens
import androidx.compose.material.icons.filled.DirectionsRun
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.DataClass.Answer
import com.example.ckc_englihoo.DataClass.Question
import com.example.ckc_englihoo.Utils.QuestionResult
import com.example.ckc_englihoo.Utils.QuestionHandler

// Classification Word với animation và màu sắc đẹp hơn
@Composable
fun ClassificationWord(
    word: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.95f else 1f,
        animationSpec = tween(durationMillis = 100)
    )

    Surface(
        modifier = Modifier
            .padding(4.dp)
            .scale(scale)
            .clickable(
                interactionSource = interactionSource,
                indication = null,
                onClick = onClick
            ),
        shape = RoundedCornerShape(12.dp),
        color = if (isSelected) Color(0xFF1976D2) else Color.White,
        border = BorderStroke(
            width = 2.dp,
            color = if (isSelected) Color(0xFF1976D2) else Color(0xFF90CAF9)
        ),
        shadowElevation = if (isSelected) 8.dp else 4.dp
    ) {
        Box(
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 12.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = word,
                color = if (isSelected) Color.White else Color(0xFF1976D2),
                fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Medium,
                fontSize = 14.sp
            )
        }
    }
}

// Classification Word với màu sắc tùy chỉnh đồng bộ
@Composable
fun ClassificationWordColored(
    word: String,
    isSelected: Boolean,
    color: Color,
    onClick: () -> Unit
) {
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.95f else 1f,
        animationSpec = tween(durationMillis = 100)
    )

    Surface(
        modifier = Modifier
            .padding(2.dp)
            .scale(scale)
            .clickable(
                interactionSource = interactionSource,
                indication = null,
                onClick = onClick
            ),
        shape = RoundedCornerShape(16.dp),
        color = if (isSelected) color else Color.White,
        border = BorderStroke(
            width = 2.dp,
            color = if (isSelected) color else color.copy(alpha = 0.6f)
        ),
        shadowElevation = if (isSelected) 8.dp else 4.dp
    ) {
        Box(
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 10.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = word,
                color = if (isSelected) Color.White else color.copy(alpha = 0.8f),
                fontWeight = if (isSelected) FontWeight.Bold else FontWeight.SemiBold,
                fontSize = 14.sp,
                maxLines = 1
            )
        }
    }
}

@Composable
fun ClassificationCategory(
    name: String,
    assignedCount: Int,
    isActive: Boolean = false,
    onClick: () -> Unit
) {
    // Định nghĩa màu sắc riêng cho từng loại từ
    val categoryColors = when (name.lowercase()) {
        "noun" -> Triple(
            Color(0xFF4CAF50), // Green
            Color(0xFFE8F5E8),
            Color(0xFF2E7D32)
        )
        "verb" -> Triple(
            Color(0xFF2196F3), // Blue
            Color(0xFFE3F2FD),
            Color(0xFF1565C0)
        )
        "adjective" -> Triple(
            Color(0xFFFF9800), // Orange
            Color(0xFFFFF3E0),
            Color(0xFFE65100)
        )
        "adverb" -> Triple(
            Color(0xFF9C27B0), // Purple
            Color(0xFFF3E5F5),
            Color(0xFF6A1B9A)
        )
        else -> Triple(
            Color(0xFF607D8B), // Blue Grey
            Color(0xFFECEFF1),
            Color(0xFF37474F)
        )
    }

    val (primaryColor, lightColor, darkColor) = categoryColors
    val borderColor = if (isActive) primaryColor else Color(0xFFE0E0E0)
    val backgroundColor = if (isActive) lightColor else Color.White
    val textColor = if (isActive) darkColor else Color(0xFF616161)

    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.87f else 0.9f,
        animationSpec = tween(durationMillis = 100)
    )

    Surface(
        modifier = Modifier
            .scale(scale)
            .clip(RoundedCornerShape(12.dp))
            .border(
                width = 2.dp,
                color = borderColor,
                shape = RoundedCornerShape(12.dp)
            )
            .clickable(
                interactionSource = interactionSource,
                indication = null,
                onClick = onClick
            ),
        color = backgroundColor,
        shadowElevation = if (isActive) 6.dp else 2.dp
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            val icon = when (name.lowercase()) {
                "noun" -> Icons.Default.Book
                "verb" -> Icons.Default.DirectionsRun
                "adjective" -> Icons.Default.ColorLens
                "adverb" -> Icons.Default.Category
                else -> Icons.Default.Category
            }

            Icon(
                imageVector = icon,
                contentDescription = name,
                tint = textColor,
                modifier = Modifier.size(32.dp)
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = name,
                color = textColor,
                fontWeight = FontWeight.SemiBold,
                fontSize = 16.sp
            )

            if (assignedCount > 0) {
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "$assignedCount word${if (assignedCount > 1) "s" else ""}",
                    color = textColor,
                    fontSize = 12.sp
                )
            }
        }
    }
}

@Composable
fun ClassificationExerciseContent(
    question: Question,
    currentAnswer: String?,
    onAnswer: (String) -> Unit
) {
    val categories = remember { listOf("noun", "verb", "adjective", "adverb") }
    var selectedWord by remember { mutableStateOf<Answer?>(null) }

    // Khôi phục trạng thái ngay lập tức khi currentAnswer thay đổi
    val classifiedWords = remember(currentAnswer, question.questions_id) {
        mutableStateMapOf<Int, String>().apply {
            currentAnswer?.let { answer ->
                if (answer.isNotEmpty()) {
                    // Parse format: "answerId:category,answerId:category,..."
                    answer.split(",").forEach { pair ->
                        val parts = pair.split(":")
                        if (parts.size == 2) {
                            val answerId = parts[0].toIntOrNull()
                            val category = parts[1]
                            if (answerId != null) {
                                this[answerId] = category
                            }
                        }
                    }
                }
            }
        }
    }

    // Lưu màu gốc của mỗi từ để giữ nguyên khi phân loại
    val wordOriginalColors = remember { mutableMapOf<Int, Color>() }

    val unassignedWords = question.answers.filter { answer ->
        !classifiedWords.containsKey(answer.answers_id)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFE3F2FD)) // Light blue background
    ) {
        // Fixed Header Section - Câu hỏi và chọn từ (cố định)
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Card(
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(containerColor = Color.White),
                elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.padding(vertical = 24.dp, horizontal = 16.dp)
                ) {
                    Text(
                        text = question.question_text,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF0D47A1),
                        lineHeight = 28.sp,
                        textAlign = TextAlign.Center
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Words Section: Từ chưa được phân loại với layout xuống hàng
            if (unassignedWords.isNotEmpty()) {
                Card(
                    shape = RoundedCornerShape(12.dp),
                    colors = CardDefaults.cardColors(containerColor = Color.White),
                    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Chọn từ để phân loại:",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = Color(0xFF1976D2),
                            modifier = Modifier.padding(bottom = 12.dp)
                        )

                        // Tạo layout xuống hàng tự động với màu sắc đẹp
                        val wordsInRows = remember(unassignedWords) {
                            val rows = mutableListOf<List<Answer>>()
                            var currentRow = mutableListOf<Answer>()
                            var currentRowLength = 0

                            unassignedWords.forEach { answer ->
                                val wordLength = answer.answer_text.length
                                // Ước tính độ rộng: mỗi ký tự ~8px + padding ~32px
                                val estimatedWidth = wordLength * 8 + 32

                                // Kiểm tra nếu thêm từ này vào hàng hiện tại có vượt quá ~280px không
                                if (currentRow.isNotEmpty() && (currentRowLength + estimatedWidth + 8) > 280) {
                                    rows.add(currentRow.toList())
                                    currentRow = mutableListOf(answer)
                                    currentRowLength = estimatedWidth
                                } else {
                                    currentRow.add(answer)
                                    currentRowLength += estimatedWidth + if (currentRow.size > 1) 8 else 0 // spacing
                                }
                            }

                            if (currentRow.isNotEmpty()) {
                                rows.add(currentRow)
                            }

                            rows
                        }

                        // Màu sắc đẹp cho các từ
                        val wordColors = remember {
                            listOf(
                                Color(0xFF2196F3), // Blue
                                Color(0xFF4CAF50), // Green
                                Color(0xFFFF9800), // Orange
                                Color(0xFF9C27B0), // Purple
                                Color(0xFFE91E63), // Pink
                                Color(0xFF00BCD4), // Cyan
                                Color(0xFF8BC34A), // Light Green
                                Color(0xFFFF5722)  // Deep Orange
                            )
                        }

                        Column(
                            verticalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            wordsInRows.forEachIndexed { rowIndex, rowWords ->
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.spacedBy(8.dp, Alignment.Start)
                                ) {
                                    rowWords.forEachIndexed { wordIndex, answer ->
                                        // Sử dụng ID của từ để tính màu cố định, không phụ thuộc vị trí
                                        val colorIndex = answer.answers_id % wordColors.size
                                        val wordColor = wordColors[colorIndex]

                                        // Lưu màu gốc của từ này (chỉ lưu 1 lần)
                                        if (!wordOriginalColors.containsKey(answer.answers_id)) {
                                            wordOriginalColors[answer.answers_id] = wordColor
                                        }

                                        ClassificationWordColored(
                                            word = answer.answer_text,
                                            isSelected = selectedWord?.answers_id == answer.answers_id,
                                            color = wordOriginalColors[answer.answers_id] ?: wordColor,
                                            onClick = {
                                                selectedWord = if (selectedWord?.answers_id == answer.answers_id) null else answer
                                            }
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // Scrollable Categories Section - Chỉ phần này được cuộn
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(horizontal = 16.dp)
        ) {

            // Categories Section: Các loại từ với màu sắc riêng biệt
            Column(
                modifier = Modifier.padding(vertical = 8.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                categories.forEach { category ->
                    val assigned = classifiedWords
                        .filterValues { it == category }
                        .keys.mapNotNull { id -> question.answers.find { it.answers_id == id } }

                    // Màu sắc cho từng category
                    val categoryColors = when (category.lowercase()) {
                        "noun" -> Triple(Color(0xFF4CAF50), Color(0xFFE8F5E8), Color(0xFF2E7D32))
                        "verb" -> Triple(Color(0xFF2196F3), Color(0xFFE3F2FD), Color(0xFF1565C0))
                        "adjective" -> Triple(Color(0xFFFF9800), Color(0xFFFFF3E0), Color(0xFFE65100))
                        "adverb" -> Triple(Color(0xFF9C27B0), Color(0xFFF3E5F5), Color(0xFF6A1B9A))
                        else -> Triple(Color(0xFF607D8B), Color(0xFFECEFF1), Color(0xFF37474F))
                    }
                    val (primaryColor, lightColor, darkColor) = categoryColors

                    Card(
                        shape = RoundedCornerShape(12.dp),
                        colors = CardDefaults.cardColors(containerColor = Color.White),
                        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.spacedBy(12.dp),
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                // Category button với màu riêng
                                Box(modifier = Modifier.width(120.dp)) {
                                    ClassificationCategory(
                                        name = category.replaceFirstChar { it.uppercase() },
                                        assignedCount = assigned.size,
                                        isActive = assigned.isNotEmpty(),
                                        onClick = {
                                            selectedWord?.let { answer ->
                                                val answerId = answer.answers_id
                                                if (classifiedWords[answerId] == category) {
                                                    classifiedWords.remove(answerId)
                                                } else {
                                                    classifiedWords[answerId] = category
                                                }
                                                val result = classifiedWords.entries.joinToString(",") { "${it.key}:${it.value}" }
                                                onAnswer(result)
                                                selectedWord = null
                                            }
                                        }
                                    )
                                }

                                // Assigned words với màu theo category
                                if (assigned.isNotEmpty()) {
                                    LazyRow(
                                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                                        modifier = Modifier.weight(1f),
                                        contentPadding = PaddingValues(horizontal = 4.dp)
                                    ) {
                                        items(assigned) { word ->
                                            // Sử dụng màu gốc của từ thay vì màu category
                                            val originalColor = wordOriginalColors[word.answers_id] ?: Color(0xFF2196F3)

                                            Box(
                                                modifier = Modifier
                                                    .border(1.dp, originalColor, RoundedCornerShape(8.dp))
                                                    .background(originalColor.copy(alpha = 0.1f), RoundedCornerShape(8.dp))
                                                    .clickable {
                                                        // Bỏ gán => trở về unassigned
                                                        classifiedWords.remove(word.answers_id)
                                                        val result = classifiedWords.entries.joinToString(",") { "${it.key}:${it.value}" }
                                                        onAnswer(result)
                                                    }
                                                    .padding(horizontal = 12.dp, vertical = 8.dp)
                                            ) {
                                                Text(
                                                    text = word.answer_text,
                                                    color = originalColor.copy(alpha = 0.8f),
                                                    fontSize = 14.sp,
                                                    fontWeight = FontWeight.Medium
                                                )
                                            }
                                        }
                                    }
                                } else {
                                    // Placeholder khi chưa có từ nào được gán
                                    Box(
                                        modifier = Modifier
                                            .weight(1f)
                                            .height(40.dp)
                                            .border(
                                                1.dp,
                                                primaryColor.copy(alpha = 0.3f),
                                                RoundedCornerShape(8.dp)
                                            )
                                            .background(
                                                lightColor.copy(alpha = 0.3f),
                                                RoundedCornerShape(8.dp)
                                            ),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Text(
                                            text = "Chưa có từ nào",
                                            color = primaryColor.copy(alpha = 0.7f),
                                            fontSize = 12.sp,
                                            fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // Thêm padding bottom để tránh bị che bởi navigation bar
            Spacer(modifier = Modifier.height(100.dp))
        }
    }
}
