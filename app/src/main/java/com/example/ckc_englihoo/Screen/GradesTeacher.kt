package com.example.ckc_englihoo.Screen

import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.navigation.NavController
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.DataClass.*
import com.example.ckc_englihoo.R
import com.example.ckc_englihoo.Utils.TopSection
import android.util.Log

// UI model for displaying lesson part scores
data class LessonPartGrade(
    val lessonPartId: Int,
    val lessonPartName: String,
    val partType: String,
    val maxScore: Double = 10.0
)

// UI model for student with their scores
data class StudentGradeDisplay(
    val studentId: Int,
    val studentName: String,
    val email: String,
    val scores: Map<Int, Double> // lesson_part_id to score
) {
    fun getAverageScore(): Double {
        return if (scores.isNotEmpty()) {
            scores.values.average()
        } else 0.0
    }

    fun getScoreForLessonPart(lessonPartId: Int): Double? {
        return scores[lessonPartId]
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GradesTeacher(
    navController: NavController,
    viewModel: AppViewModel,
    courseId: Int
) {
    // Collect states from ViewModel
    val currentTeacher by viewModel.currentTeacher.collectAsState()
    val teacherCourses by viewModel.teacherCourses.collectAsState()
    val teacherCourseStudents by viewModel.teacherCourseStudents.collectAsState()
    val lessonParts by viewModel.lessonParts.collectAsState()
    val lessonPartScores by viewModel.lessonPartScores.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()

    // Get course from teacher courses and students for this course
    val course = teacherCourses.find { it.course_id == courseId }
    val courseStudents = teacherCourseStudents[courseId] ?: emptyList()

    // Track loading state for scores
    var scoresLoaded by remember { mutableStateOf(false) }

    // Load teacher courses first
    LaunchedEffect(currentTeacher) {
        currentTeacher?.let { teacher ->
            Log.d("GradesTeacher", "Loading teacher courses for teacher ${teacher.teacher_id}")
            viewModel.loadTeacherCourses(teacher.teacher_id)
        }
    }

    // Load lesson parts for the specific course immediately
    LaunchedEffect(courseId) {
        Log.d("GradesTeacher", "Loading lesson parts for course $courseId")
        viewModel.loadLessonPartsByCourse(courseId)
        viewModel.loadLessonPartScoresByCourse(courseId)
    }

    // Load scores for each student when courseStudents becomes available
    LaunchedEffect(courseStudents) {
        if (courseStudents.isNotEmpty() && !scoresLoaded) {
            Log.d("GradesTeacher", "Loading scores for ${courseStudents.size} students")
            courseStudents.forEach { student ->
                Log.d("GradesTeacher", "Loading scores for student ${student.student_id}: ${student.fullname}")
                viewModel.loadScoresByStudentId(student.student_id)
            }
            scoresLoaded = true
        }
    }

    // Convert API data to UI models
    val lessonPartGrades = remember(lessonParts) {
        lessonParts.map { lessonPart ->
            LessonPartGrade(
                lessonPartId = lessonPart.lesson_part_id,
                lessonPartName = "Phần ${lessonPart.lesson_part_id}",
                partType = when (lessonPart.part_type) {
                    "listening" -> "Nghe"
                    "reading" -> "Đọc"
                    "speaking" -> "Nói"
                    "writing" -> "Viết"
                    "quiz" -> "Kiểm tra"
                    else -> lessonPart.part_type
                }
            )
        }
    }

    // Create student grades from courseStudents and available scores
    val studentGrades = remember(courseStudents, lessonPartScores) {
        courseStudents.map { student ->
            // Filter scores for this student and course
            val studentScores = lessonPartScores.filter { score ->
                score.student_id == student.student_id &&
                lessonParts.any { it.lesson_part_id == score.lesson_part_id }
            }

            val scoresMap = studentScores.associate { score ->
                score.lesson_part_id to score.score
            }

            Log.d("GradesTeacher", "Student ${student.fullname}: ${scoresMap.size} scores loaded")

            StudentGradeDisplay(
                studentId = student.student_id,
                studentName = student.fullname,
                email = student.email,
                scores = scoresMap
            )
        }
    }

    Log.d("GradesTeacher", "Lesson parts: ${lessonPartGrades.size}, Students: ${studentGrades.size}")

    Scaffold(
        topBar = {
            TopSection(
                title = "Điểm số - ${course?.course_name ?: "Khóa học"}",
                showBackButton = true,
                onBackClick = { navController.popBackStack() },
                backgroundColor = Color(0xFF2E7D32) // Green for teacher
            )
        }
    ) { padding ->
        Column(
            Modifier
                .fillMaxSize()
                .background(Color(0xFFF5F5F5))
                .padding(padding)
        ) {
            when {
                isLoading || (teacherCourses.isEmpty()) -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            CircularProgressIndicator()
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                text = when {
                                    teacherCourses.isEmpty() -> "Đang tải khóa học..."
                                    courseStudents.isEmpty() -> "Đang tải danh sách học sinh..."
                                    else -> "Đang tải dữ liệu..."
                                },
                                fontSize = 16.sp,
                                color = Color.Gray
                            )
                        }
                    }
                }
                course == null -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "Không tìm thấy khóa học",
                                fontSize = 16.sp,
                                color = Color.Gray
                            )
                            Text(
                                text = "Bạn không có quyền truy cập khóa học này",
                                fontSize = 14.sp,
                                color = Color.Gray
                            )
                        }
                    }
                }
                courseStudents.isEmpty() -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "Chưa có học sinh nào",
                                fontSize = 16.sp,
                                color = Color.Gray
                            )
                            Text(
                                text = "Khóa học chưa có học sinh đăng ký",
                                fontSize = 14.sp,
                                color = Color.Gray
                            )
                        }
                    }
                }
                else -> {
                    GradesStatisticsSection(studentGrades, lessonPartGrades)
                    GradesDisplaySection(lessonPartGrades, studentGrades)
                }
            }
        }
    }
}

@Composable
private fun GradesStatisticsSection(
    students: List<StudentGradeDisplay>,
    lessonParts: List<LessonPartGrade>
) {
    val avgScore = if (students.isNotEmpty()) {
        students.map { it.getAverageScore() }.average()
    } else 0.0

    val completedStudents = students.count { student ->
        lessonParts.all { lessonPart ->
            student.getScoreForLessonPart(lessonPart.lessonPartId) != null
        }
    }

    StatisticRow(
        listOf(
            Triple("Điểm TB", String.format("%.1f", avgScore), Color(0xFF2E7D32)),
            Triple("Hoàn thành", "$completedStudents/${students.size}", Color(0xFF4CAF50)),
            Triple("Bài tập", "${lessonParts.size}", Color(0xFF2196F3))
        )
    )
}

@Composable
private fun StatisticRow(items: List<Triple<String, String, Color>>) =
    Card(
        Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Row(
            Modifier.padding(16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            items.forEach { (title, value, color) ->
                GradeStatisticItem(value, title, color)
            }
        }
    }

@Composable
private fun GradeStatisticItem(value: String, title: String, color: Color) =
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Text(
            value,
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            title,
            fontSize = 12.sp,
            color = Color.Gray
        )
    }

@Composable
private fun GradesDisplaySection(
    lessonParts: List<LessonPartGrade>,
    students: List<StudentGradeDisplay>
) = Card(
    Modifier
        .fillMaxWidth()
        .padding(horizontal = 16.dp),
    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
) {
    Column {
        GradesTableHeader(lessonParts)
        Divider()
        GradesTableBody(lessonParts, students)
    }
}

@Composable
private fun GradesTableHeader(lessonParts: List<LessonPartGrade>) =
    Row(
        Modifier
            .background(Color(0xFFF5F5F5))
            .horizontalScroll(rememberScrollState())
            .padding(vertical = 8.dp)
    ) {
        TableCell("Học sinh", 150.dp, fontWeight = FontWeight.Bold)
        lessonParts.forEach { lessonPart ->
            TableCell(lessonPart.partType, 100.dp, fontWeight = FontWeight.Bold)
        }
        TableCell("Điểm TB", 80.dp, fontWeight = FontWeight.Bold)
    }

@Composable
private fun GradesTableBody(
    lessonParts: List<LessonPartGrade>,
    students: List<StudentGradeDisplay>
) = LazyColumn {
    items(students) { student ->
        Row(
            Modifier.horizontalScroll(rememberScrollState())
        ) {
            TableCell(
                text = student.studentName,
                width = 150.dp,
                leading = { AvatarCell(R.drawable.student) }
            )
            lessonParts.forEach { lessonPart ->
                ScoreDisplayCell(student.getScoreForLessonPart(lessonPart.lessonPartId))
            }
            TableCell(
                String.format("%.1f", student.getAverageScore()),
                80.dp,
                fontWeight = FontWeight.Medium
            )
        }
        Divider()
    }
}

@Composable
private fun TableCell(
    text: String,
    width: androidx.compose.ui.unit.Dp,
    leading: @Composable (() -> Unit)? = null,
    fontWeight: FontWeight = FontWeight.Normal
) = Box(
    Modifier.width(width).padding(8.dp),
    contentAlignment = Alignment.Center
) {
    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        leading?.invoke()
        if (leading != null) Spacer(Modifier.width(8.dp))
        Text(
            text,
            overflow = TextOverflow.Ellipsis,
            maxLines = 1,
            fontWeight = fontWeight,
            fontSize = 14.sp
        )
    }
}

@Composable
private fun AvatarCell(res: Int) =
    Image(
        painterResource(res),
        null,
        Modifier
            .size(32.dp)
            .clip(CircleShape)
    )

@Composable
private fun ScoreDisplayCell(score: Double?) = Box(
    Modifier
        .width(100.dp)
        .padding(8.dp),
    contentAlignment = Alignment.Center
) {
    val displayText = score?.let { String.format("%.1f", it) } ?: "-"
    val textColor = when {
        score == null -> Color.Gray
        score >= 8.0 -> Color(0xFF4CAF50) // Green for good scores
        score >= 6.0 -> Color(0xFFFF9800) // Orange for average scores
        else -> Color(0xFFF44336) // Red for low scores
    }

    Text(
        displayText,
        overflow = TextOverflow.Ellipsis,
        color = textColor,
        fontWeight = if (score != null) FontWeight.Medium else FontWeight.Normal,
        fontSize = 14.sp
    )
}


