package com.example.ckc_englihoo.Screen

import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.navigation.NavController
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.DataClass.*
import com.example.ckc_englihoo.R
import com.example.ckc_englihoo.Utils.TeacherTopSection
import kotlinx.coroutines.launch
import android.util.Log
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreenTeacher(
    navController: NavController,
    viewModel: AppViewModel,
    onClassClick: (Course) -> Unit = {},
    onCreateClassClick: () -> Unit = {},
    onProfileClick: () -> Unit = {}
) {
    val currentTeacher by viewModel.currentTeacher.collectAsState()
    val courses by viewModel.courses.collectAsState()
    val teacherCourses by viewModel.teacherCourses.collectAsState()
    val notifications by viewModel.notifications.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val courseStudentCounts by viewModel.courseStudentCounts.collectAsState()

    val notificationsForDisplay = remember(notifications) {
        getNotificationsForDisplay(notifications ?: emptyList())
    }
    // Load teacher data and courses when teacher is available
    LaunchedEffect(currentTeacher) {
        currentTeacher?.let { teacher ->
            Log.d("HomeScreenTeacher", "Loading data for teacher: ${teacher.fullname} (ID: ${teacher.teacher_id})")
            viewModel.loadAllCourses()
            viewModel.loadTeacherCourses(teacher.teacher_id)
        }
        // Load notifications with error handling
        try {
            viewModel.loadStudentNotifications(1)
        } catch (e: Exception) {
            Log.e("HomeScreenStudent", "Error loading notifications", e)
            // Continue without notifications
        }
    }
    LaunchedEffect(teacherCourses) {
        if (teacherCourses.isNotEmpty()) {
            val ids = teacherCourses.map { it.course_id }
            viewModel.loadMultipleCourseStudentCounts(ids)
        }
    }

    Scaffold(
        topBar = {
            currentTeacher?.let { teacher ->
                TeacherTopSection(
                    teacher = teacher,
                    title = "Trang chủ",
                    onProfileClick = onProfileClick,
                    onLogoutClick = {
                        // Handle logout
                        viewModel.logout()
                        navController.navigate("login") {
                            popUpTo(0) { inclusive = true }
                        }
                    }
                )
            }
        },
    ) { padding ->
        Column(
            Modifier
                .fillMaxSize()
                .padding(padding)
                .background(Color(0xFFF5F5F5))
        ) {
            Spacer(Modifier.height(16.dp))

            // Show loading indicator
            if (isLoading) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(60.dp),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(modifier = Modifier.size(24.dp))
                }
            }

            // Main content
            if (teacherCourses.isEmpty() && !isLoading) {
                EmptyState { onCreateClassClick() }
            } else {
                ClassList(
                    courses = teacherCourses,
                    courseStudentCounts = courseStudentCounts,
                    onClassClick = onClassClick
                )
            }
            NotificationsColumn(
                notifications = notificationsForDisplay,
                onViewAllClick = {
                    navController.navigate("notifications")
                }
            )
        }
    }
}


@Composable
private fun EmptyState(onCreate: () -> Unit) =
    Column(
        Modifier
            .fillMaxSize()
            .padding(32.dp), 
        horizontalAlignment = Alignment.CenterHorizontally, 
        verticalArrangement = Arrangement.Center
    ) {
        IconGrid(listOf(Icons.Default.MenuBook, Icons.Default.Edit, Icons.Default.School, Icons.Default.Wifi))
        Spacer(Modifier.height(32.dp))
        Text(
            "Chưa có lớp học nào", 
            fontSize = 18.sp, 
            fontWeight = FontWeight.Medium, 
            color = Color(0xFF666666)
        )
        Spacer(Modifier.height(8.dp))
        Button(
            onClick = onCreate, 
            colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF1976D2))
        ) {
            Text("Tạo lớp học", color = Color.White)
        }
    }

@Composable
private fun IconGrid(icons: List<ImageVector>) =
    Row(horizontalArrangement = Arrangement.spacedBy(16.dp)) {
        icons.forEach { 
            Icon(
                it, 
                null, 
                tint = Color(0xFFBDBDBD), 
                modifier = Modifier.size(32.dp)
            ) 
        }
    }

@Composable
private fun ClassList(
    courses: List<Course>,
    courseStudentCounts: Map<Int, Int>,
    onClassClick: (Course) -> Unit
) = LazyColumn(
    Modifier
        .fillMaxSize()
        .padding(16.dp),
    verticalArrangement = Arrangement.spacedBy(16.dp)
) {
    item {
        Text(
            "Khóa học của tôi",
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF333333),
            modifier = Modifier.padding(vertical = 8.dp)
        )
    }
    items(courses) { course ->
        ClassCard(course, studentCount = courseStudentCounts[course.course_id] ?: course.total_students,) { onClassClick(course) }
    }
    item { Spacer(Modifier.height(80.dp)) }
}


@Composable
private fun ClassCard(
    course: Course,
    studentCount:Int,
    onClick: () -> Unit
) {
    val backgroundColor = Color(0xFF1976D2) // Default blue color
    
    Card(
        Modifier
            .fillMaxWidth()
            .clickable { onClick() }, 
        shape = RoundedCornerShape(12.dp), 
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column {
            Box(
                Modifier
                    .height(120.dp)
                    .fillMaxWidth()
                    .background(
                        Brush.horizontalGradient(
                            listOf(backgroundColor, backgroundColor.copy(alpha = 0.8f))
                        ), 
                        RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)
                    )
            ) {
                Column(Modifier.padding(16.dp)) {
                    Text(
                        course.course_name, 
                        fontSize = 18.sp, 
                        fontWeight = FontWeight.Bold, 
                        color = Color.White, 
                        maxLines = 1, 
                        overflow = TextOverflow.Ellipsis
                    )
                    Text(
                        course.description, 
                        fontSize = 14.sp, 
                        color = Color.White.copy(alpha = 0.9f), 
                        maxLines = 2, 
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
            Row(
                Modifier.padding(16.dp), 
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.Person, 
                    null, 
                    tint = Color.Gray, 
                    modifier = Modifier.size(20.dp)
                )
                Spacer(Modifier.width(8.dp))
                Text(
                    "${studentCount ?: 0} học sinh",
                    fontSize = 14.sp, 
                    color = Color.Gray
                )
            }
        }
    }
}



