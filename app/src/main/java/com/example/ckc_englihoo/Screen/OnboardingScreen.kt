package com.example.ckc_englihoo.Screen

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.ckc_englihoo.R
import kotlinx.coroutines.launch

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun OnboardingScreen(
    navController: NavController
) {
    val pages = listOf(
        OnboardingPage(
            image = R.drawable.image2,
            title = "Học bài và làm bài tập trực tuyến",
            description = "Bạn có thể học các bài giảng, làm bài tập và xem video minh hoạ cho từng chủ đề học ngay trên ứng dụng."
        ),
        OnboardingPage(
            image = R.drawable.image3,
            title = "Thư viện học liệu đa dạng",
            description = "Ứng dụng tích hợp đầy đủ tài liệu học tiếng Anh cho mọi trình độ, có thể sử dụng mọi lúc, mọi nơi."
        ),
        OnboardingPage(
            image = R.drawable.image4,
            title = "Trau dồi từ vựng theo chủ đề",
            description = " Kho từ vựng phong phú được phân loại theo từng chủ đề, giúp bạn học dễ nhớ và áp dụng hiệu quả."
        ),
        OnboardingPage(
            image = R.drawable.image5,
            title = "Tham gia sự kiện dễ dàng",
            description = "Bạn có thể xem và đăng ký các sự kiện học tập ngay trên ứng dụng, không cần đăng ký trực tiếp hay giấy tờ."
        ),
        OnboardingPage(
            image = R.drawable.image6,
            title = "Xem kết quả thi",
            description = "Bạn có thể xem điểm thi và kết quả học tập của mình theo từng môn, cấp độ và thời gian ngay trên ứng dụng."
        ),
        OnboardingPage(
            image = R.drawable.image7,
            title = "Không gian học tập cá nhân",
            description = "Đây là nơi bạn quản lý toàn bộ hoạt động học tập của mình: theo dõi tiến độ, truy cập bài học, lịch sử học tập và điều chỉnh lộ trình phù hợp với mục tiêu cá nhân."
        )
    )

    val pagerState = rememberPagerState(pageCount = { pages.size })
    val scope = rememberCoroutineScope()

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(40.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Sử dụng HorizontalPager để hỗ trợ vuốt
        HorizontalPager(
            state = pagerState,
            modifier = Modifier.weight(1f)
        ) { page ->
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.fillMaxWidth()
            ) {
                // Hình ảnh minh họa
                Image(
                    painter = painterResource(id = pages[page].image),
                    contentDescription = "Onboarding Image",
                    modifier = Modifier
                        .size(240.dp)
                        .padding(16.dp)
                )

                Spacer(modifier = Modifier.height(24.dp))

                // Tiêu đề
                Text(
                    text = pages[page].title,
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Mô tả
                Text(
                    text = pages[page].description,
                    fontSize = 16.sp,
                    color = Color.Gray,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(horizontal = 16.dp)
                )
            }
        }

        Spacer(modifier = Modifier.height(12.dp))

        // Indicator dots và nút điều hướng
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Indicator dots
            Row(
                horizontalArrangement = Arrangement.Center,
                modifier = Modifier.padding(vertical = 16.dp)
            ) {
                repeat(pages.size) { index ->
                    Box(
                        modifier = Modifier
                            .padding(horizontal = 6.dp)
                            .size(12.dp)
                            .background(
                                color = if (pagerState.currentPage == index) Color(0xFF4355EE) else Color.LightGray,
                                shape = CircleShape
                            )
                            .clickable {
                                scope.launch {
                                    pagerState.animateScrollToPage(index)
                                }
                            }
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // Nút Login
        Button(
            onClick = {
                navController.navigate("login") {
                    popUpTo("onboarding") { inclusive = true }
                }
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(50.dp),
            shape = RoundedCornerShape(8.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFF4355EE)
            )
        ) {
            Text(
                text = "Đăng Nhập",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold
            )
        }

        Spacer(modifier = Modifier.height(24.dp))
    }
}

data class OnboardingPage(
    val image: Int,
    val title: String,
    val description: String
)
