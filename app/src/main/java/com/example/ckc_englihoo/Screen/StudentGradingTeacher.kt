package com.example.ckc_englihoo.Screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.DataClass.*

// Data classes for grading
data class GradeItem(
    val id: String,
    val title: String,
    val description: String,
    val currentScore: Int,
    val maxScore: Int,
    val type: GradeType,
    val date: String
)

enum class GradeType {
    HOMEWORK, QUIZ, EXAM, PARTICIPATION
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StudentGradingTeacher(
    navController: NavController,
    viewModel: AppViewModel,
    studentId: Int,
    courseId: Int
) {
    // Collect states from ViewModel
    val currentTeacher by viewModel.currentTeacher.collectAsState()
    val teacherCourses by viewModel.teacherCourses.collectAsState()
    val teacherCourseStudents by viewModel.teacherCourseStudents.collectAsState()
    val examResults by viewModel.examResults.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()

    // Get course from teacher courses and students for this course
    val course = teacherCourses.find { it.course_id == courseId }
    val courseStudents = teacherCourseStudents[courseId] ?: emptyList()
    val student = courseStudents.find { it.student_id == studentId }

    // Load teacher courses and exam results
    LaunchedEffect(currentTeacher) {
        currentTeacher?.let { teacher ->
            viewModel.loadTeacherCourses(teacher.teacher_id)
        }
    }

    LaunchedEffect(courseId) {
        viewModel.loadStudentsByEnrollment(courseId)
        viewModel.loadExamResultsByCourseAndStudent(courseId, studentId)
    }
    // State for exam result editing
    var showExamDialog by remember { mutableStateOf(false) }
    var editingExamResult by remember { mutableStateOf<ExamResult?>(null) }

    // Get current exam result for this student and course
    val currentExamResult = examResults.find {
        it.student_id == studentId && it.course_id == courseId
    }

    val studentName = student?.fullname ?: "Học sinh $studentId"
    val courseName = course?.course_name ?: "Khóa học $courseId"

    Column(Modifier.fillMaxSize()) {
        GradientBar(
            title = "Cho điểm - $studentName",
            subtitle = courseName,
            onBack = { navController.popBackStack() }
        )

        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                item {
                    StudentInfoCard(
                        student = student,
                        course = course
                    )
                }

                item {
                    ExamResultCard(
                        examResult = currentExamResult,
                        onEdit = {
                            editingExamResult = currentExamResult
                            showExamDialog = true
                        },
                        onCreate = {
                            editingExamResult = null
                            showExamDialog = true
                        }
                    )
                }

                item { Spacer(Modifier.height(80.dp)) }
            }
        }

        // Show exam result dialog
        if (showExamDialog) {
            ExamResultDialog(
                examResult = editingExamResult,
                student = student,
                course = course,
                onDismiss = {
                    showExamDialog = false
                    editingExamResult = null
                },
                onSave = { listeningScore, speakingScore, readingScore, writingScore, overallStatus ->
                    val examId = editingExamResult?.exam_result_id ?: System.currentTimeMillis().toInt()
                    val examDate = editingExamResult?.exam_date
                        ?: SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())

                    viewModel.submitExamResult(
                        examId = examId,
                        studentId = studentId,
                        courseId = courseId,
                        examDate = examDate,
                        listeningScore = listeningScore,
                        speakingScore = speakingScore,
                        readingScore = readingScore,
                        writingScore = writingScore,
                        overallStatus = overallStatus,
                        onSuccess = { _, _ ->
                            showExamDialog = false
                            editingExamResult = null
                        },
                        onError = { error ->
                            // Handle error - could show snackbar
                        }
                    )
                }
            )
        }

        // Show error message if any
        errorMessage?.let { error ->
            LaunchedEffect(error) {
                // Could show snackbar here
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun GradientBar(
    title: String,
    subtitle: String = "",
    onBack: () -> Unit
) = TopAppBar(
    title = {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                title,
                textAlign = TextAlign.Center,
                color = Color.White,
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold
            )
            if (subtitle.isNotEmpty()) {
                Text(
                    subtitle,
                    textAlign = TextAlign.Center,
                    color = Color.White.copy(alpha = 0.8f),
                    fontSize = 14.sp
                )
            }
        }
    },
    navigationIcon = {
        IconButton(onClick = onBack) {
            Icon(Icons.Default.ArrowBack, null, tint = Color.White)
        }
    },
    colors = TopAppBarDefaults.topAppBarColors(containerColor = Color.Transparent),
    modifier = Modifier.background(
        Brush.verticalGradient(
            listOf(Color(0xFF1976D2), Color(0xFF2196F3))
        )
    )
)

@Composable
private fun SummaryCard(total: Int, max: Int) {
    val percentage = if (max > 0) total * 100 / max else 0
    val percentageColor = when {
        percentage >= 80 -> Color(0xFF4CAF50)
        percentage >= 60 -> Color(0xFFFF9800)
        else -> Color(0xFFF44336)
    }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    total.toString(),
                    fontSize = 48.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2196F3)
                )
                Text(
                    "/$max",
                    fontSize = 24.sp,
                    color = Color.Gray
                )
            }
            Spacer(Modifier.height(8.dp))
            Text(
                "$percentage%",
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                color = percentageColor
            )
            Spacer(Modifier.height(16.dp))
            LinearProgressIndicator(
                progress = { percentage / 100f },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(8.dp)
                    .clip(RoundedCornerShape(4.dp)),
                color = Color(0xFF2196F3),
                trackColor = Color(0xFFE3F2FD)
            )
        }
    }
}

@Composable
private fun GradeCard(item: GradeItem) {
    var showEditDialog by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        item.title,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        item.description,
                        color = Color.Gray
                    )
                    Text(
                        item.date,
                        fontSize = 12.sp,
                        color = Color.LightGray
                    )
                }
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        "${item.currentScore}/${item.maxScore}",
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF2196F3)
                    )
                    IconButton(onClick = { showEditDialog = true }) {
                        Icon(
                            Icons.Default.Edit,
                            null,
                            tint = Color(0xFF2196F3)
                        )
                    }
                }
            }
            Spacer(Modifier.height(8.dp))
            GradeTypeBadge(item.type)
        }
    }
    
    if (showEditDialog) {
        ScoreEditDialog(
            item = item,
            onDismiss = { showEditDialog = false }
        )
    }
}

@Composable
private fun StudentInfoCard(
    student: Student?,
    course: Course?
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                "Thông tin học sinh",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF1976D2)
            )
            Spacer(Modifier.height(12.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text("Tên học sinh:", fontSize = 14.sp, color = Color.Gray)
                    Text(
                        student?.fullname ?: "N/A",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
                Column(modifier = Modifier.weight(1f)) {
                    Text("Khóa học:", fontSize = 14.sp, color = Color.Gray)
                    Text(
                        course?.course_name ?: "N/A",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }

            Spacer(Modifier.height(8.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text("Cấp độ:", fontSize = 14.sp, color = Color.Gray)
                    Text(
                        course?.level ?: "N/A",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
                Column(modifier = Modifier.weight(1f)) {
                    Text("Năm học:", fontSize = 14.sp, color = Color.Gray)
                    Text(
                        course?.year ?: "N/A",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}

@Composable
private fun ExamResultCard(
    examResult: ExamResult?,
    onEdit: () -> Unit,
    onCreate: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    "Kết quả thi",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF1976D2)
                )

                if (examResult != null) {
                    IconButton(onClick = onEdit) {
                        Icon(
                            Icons.Default.Edit,
                            contentDescription = "Chỉnh sửa",
                            tint = Color(0xFF1976D2)
                        )
                    }
                } else {
                    Button(
                        onClick = onCreate,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF1976D2)
                        )
                    ) {
                        Icon(Icons.Default.Add, contentDescription = null)
                        Spacer(Modifier.width(4.dp))
                        Text("Tạo mới")
                    }
                }
            }

            Spacer(Modifier.height(12.dp))

            if (examResult != null) {
                // Show exam scores
                Column {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        ScoreItemDisplay("Nghe", examResult.listening_score)
                        ScoreItemDisplay("Nói", examResult.speaking_score)
                    }
                    Spacer(Modifier.height(8.dp))
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        ScoreItemDisplay("Đọc", examResult.reading_score)
                        ScoreItemDisplay("Viết", examResult.writing_score)
                    }
                    Spacer(Modifier.height(12.dp))

                    // Average score and status
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column {
                            Text("Điểm trung bình:", fontSize = 14.sp, color = Color.Gray)
                            Text(
                                String.format("%.1f/10", examResult.average_score),
                                fontSize = 18.sp,
                                fontWeight = FontWeight.Bold,
                                color = Color(0xFF1976D2)
                            )
                        }

                        val statusColor = if (examResult.overall_status == 1) Color(0xFF4CAF50) else Color(0xFFF44336)
                        val statusText = if (examResult.overall_status == 1) "Đạt" else "Chưa đạt"

                        Card(
                            colors = CardDefaults.cardColors(containerColor = statusColor.copy(alpha = 0.1f))
                        ) {
                            Text(
                                statusText,
                                modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
                                color = statusColor,
                                fontWeight = FontWeight.Bold
                            )
                        }
                    }

                    Spacer(Modifier.height(8.dp))
                    Text(
                        "Ngày thi: ${examResult.exam_date}",
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                }
            } else {
                // No exam result yet
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(
                        Icons.Default.Assignment,
                        contentDescription = null,
                        modifier = Modifier.size(48.dp),
                        tint = Color.Gray
                    )
                    Spacer(Modifier.height(8.dp))
                    Text(
                        "Chưa có kết quả thi",
                        fontSize = 16.sp,
                        color = Color.Gray,
                        textAlign = TextAlign.Center
                    )
                    Text(
                        "Nhấn 'Tạo mới' để thêm kết quả thi",
                        fontSize = 14.sp,
                        color = Color.Gray,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
}



@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ExamResultDialog(
    examResult: ExamResult?,
    student: Student?,
    course: Course?,
    onDismiss: () -> Unit,
    onSave: (Double, Double, Double, Double, Int) -> Unit
) {
    var listeningScore by remember { mutableStateOf(examResult?.listening_score?.toString() ?: "") }
    var speakingScore by remember { mutableStateOf(examResult?.speaking_score?.toString() ?: "") }
    var readingScore by remember { mutableStateOf(examResult?.reading_score?.toString() ?: "") }
    var writingScore by remember { mutableStateOf(examResult?.writing_score?.toString() ?: "") }
    var overallStatus by remember { mutableStateOf(examResult?.overall_status ?: 0) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                if (examResult != null) "Chỉnh sửa kết quả thi" else "Tạo kết quả thi mới",
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text(
                    "Học sinh: ${student?.fullname ?: "N/A"}",
                    fontSize = 14.sp,
                    color = Color.Gray
                )
                Text(
                    "Khóa học: ${course?.course_name ?: "N/A"}",
                    fontSize = 14.sp,
                    color = Color.Gray
                )

                Spacer(Modifier.height(8.dp))

                // Score input fields
                OutlinedTextField(
                    value = listeningScore,
                    onValueChange = { listeningScore = it },
                    label = { Text("Điểm Nghe (0-10)") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    modifier = Modifier.fillMaxWidth()
                )

                OutlinedTextField(
                    value = speakingScore,
                    onValueChange = { speakingScore = it },
                    label = { Text("Điểm Nói (0-10)") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    modifier = Modifier.fillMaxWidth()
                )

                OutlinedTextField(
                    value = readingScore,
                    onValueChange = { readingScore = it },
                    label = { Text("Điểm Đọc (0-10)") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    modifier = Modifier.fillMaxWidth()
                )

                OutlinedTextField(
                    value = writingScore,
                    onValueChange = { writingScore = it },
                    label = { Text("Điểm Viết (0-10)") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    modifier = Modifier.fillMaxWidth()
                )

                // Overall status
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text("Kết quả tổng thể:", modifier = Modifier.weight(1f))
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = overallStatus == 1,
                            onClick = { overallStatus = 1 }
                        )
                        Text("Đạt", modifier = Modifier.padding(end = 16.dp))

                        RadioButton(
                            selected = overallStatus == 0,
                            onClick = { overallStatus = 0 }
                        )
                        Text("Chưa đạt")
                    }
                }
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    val listening = listeningScore.toDoubleOrNull() ?: 0.0
                    val speaking = speakingScore.toDoubleOrNull() ?: 0.0
                    val reading = readingScore.toDoubleOrNull() ?: 0.0
                    val writing = writingScore.toDoubleOrNull() ?: 0.0

                    if (listening in 0.0..10.0 && speaking in 0.0..10.0 &&
                        reading in 0.0..10.0 && writing in 0.0..10.0) {
                        onSave(listening, speaking, reading, writing, overallStatus)
                    }
                },
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF1976D2))
            ) {
                Text("Lưu")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Hủy")
            }
        }
    )
}

@Composable
private fun GradeTypeBadge(type: GradeType) {
    val (text, color) = when (type) {
        GradeType.HOMEWORK -> "Bài tập" to Color(0xFF4CAF50)
        GradeType.QUIZ -> "Kiểm tra" to Color(0xFFFF9800)
        GradeType.EXAM -> "Thi" to Color(0xFFF44336)
        GradeType.PARTICIPATION -> "Tham gia" to Color(0xFF9C27B0)
    }
    
    Text(
        text = text,
        fontSize = 12.sp,
        color = Color.White,
        modifier = Modifier
            .background(
                color = color,
                shape = RoundedCornerShape(12.dp)
            )
            .padding(horizontal = 8.dp, vertical = 4.dp)
    )
}

@Composable
private fun ScoreEditDialog(
    item: GradeItem,
    onDismiss: () -> Unit
) {
    var scoreText by remember { mutableStateOf(item.currentScore.toString()) }
    var isValid by remember { mutableStateOf(true) }
    
    LaunchedEffect(scoreText) {
        isValid = scoreText.toIntOrNull()?.let { it in 0..item.maxScore } == true
    }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                "Chỉnh sửa điểm",
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF1976D2)
            )
        },
        text = {
            Column {
                Text(
                    "Bài: ${item.title}",
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                OutlinedTextField(
                    value = scoreText,
                    onValueChange = { scoreText = it },
                    label = { Text("Điểm (tối đa ${item.maxScore})") },
                    isError = !isValid,
                    supportingText = if (!isValid) {
                        { Text("Điểm phải từ 0 đến ${item.maxScore}", color = Color.Red) }
                    } else null,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    if (isValid) {
                        // TODO: Save score to backend
                        onDismiss()
                    }
                }
            ) {
                Text("Lưu")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Hủy")
            }
        },
        shape = RoundedCornerShape(16.dp)
    )
}

@Composable
private fun RowScope.ScoreItemDisplay(label: String, score: Double) {
    Column(
        modifier = Modifier.weight(1f),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(label, fontSize = 14.sp, color = Color.Gray)
        Text(
            String.format("%.1f", score),
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF1976D2)
        )
    }
}

