package com.example.ckc_englihoo.Screen

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.pager.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.filled.HourglassEmpty
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.Screen.Exercises.*
import com.example.ckc_englihoo.Utils.QuestionNavigationControls
import com.example.ckc_englihoo.Utils.QuestionProgressBar
import com.example.ckc_englihoo.Utils.SubmitConfirmationDialog
import com.example.ckc_englihoo.Components.ValidationErrorDialog
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun QuestionScreenWithTabs(
    navController: NavController,
    lessonPartId: Int,
    lessonPartTitle: String,
    studentId: Int,
    courseId: Int,
    viewModel: AppViewModel
) {
    val questions by viewModel.questions.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()
    val tempAnswers by viewModel.tempStudentAnswers.collectAsState()

    var currentQuestionIndex by remember { mutableIntStateOf(0) }
    var showSubmitDialog by remember { mutableStateOf(false) }
    var showValidationDialog by remember { mutableStateOf(false) }
    var validationErrors by remember { mutableStateOf<List<String>>(emptyList()) }
    var submissionTime by remember { mutableStateOf("") }

    LaunchedEffect(lessonPartId) {
        viewModel.loadQuestionsByLessonPart(lessonPartId)
    }

    // Xóa questions khi rời khỏi màn hình
    DisposableEffect(Unit) {
        onDispose {
            viewModel.clearQuestions()
            viewModel.clearTempStudentAnswers() // Xóa đáp án tạm thời khi thoát
        }
    }

    val currentQuestion = questions.getOrNull(currentQuestionIndex)

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(lessonPartTitle) },
                navigationIcon = {
                    IconButton(onClick = {
                        navController.popBackStack()
                    }) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color(0xFF2196F3),
                    titleContentColor = Color.White,
                    navigationIconContentColor = Color.White
                )
            )
        },
        bottomBar = {
            if (!isLoading && errorMessage == null && currentQuestion != null) {
                Surface(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(130.dp),
                    color = Color.White,
                    shadowElevation = 8.dp
                ) {
                    QuestionNavigationControls(
                        currentIndex = currentQuestionIndex,
                        totalQuestions = questions.size,
                        onPrevious = { if (currentQuestionIndex > 0) currentQuestionIndex-- },
                        onNext = {
                            if (currentQuestionIndex < questions.size - 1) {
                                currentQuestionIndex++
                            } else {
                                showSubmitDialog = true
                            }
                        },
                        onSubmit = { showSubmitDialog = true }
                    )
                }
            }
        }
    ) { paddingValues ->
        Column(modifier = Modifier
            .fillMaxSize()
            .padding(paddingValues)) {
            QuestionProgressBar(
                current = currentQuestionIndex + 1,
                total = questions.size,
                modifier = Modifier.padding(16.dp)
            )

            when {
                isLoading -> FullScreenLoading()
                errorMessage != null -> ErrorScreen(errorMessage!!) { viewModel.loadQuestionsByLessonPart(lessonPartId) }
                currentQuestion == null -> EmptyQuestionScreen()
                else -> {
                    // Truyền currentAnswer từ tempAnswers vào từng ExerciseContent
                    when (currentQuestion.question_type) {
                        "single_choice" -> SingleChoiceExerciseContent(
                            question = currentQuestion,
                            currentAnswer = tempAnswers[currentQuestion.questions_id]?.answer_text,
                            onAnswer = { answer ->
                                viewModel.saveStudentAnswer(currentQuestion.questions_id, answer)
                            }
                        )
                        "matching" -> MatchingExerciseContent(
                            question = currentQuestion,
                            currentAnswer = tempAnswers[currentQuestion.questions_id]?.answer_text,
                            onAnswer = { answer ->
                                viewModel.saveStudentAnswer(currentQuestion.questions_id, answer)
                            }
                        )
                        "classification" -> ClassificationExerciseContent(
                            question = currentQuestion,
                            currentAnswer = tempAnswers[currentQuestion.questions_id]?.answer_text,
                            onAnswer = { answer ->
                                viewModel.saveStudentAnswer(currentQuestion.questions_id, answer)
                            }
                        )
                        "fill_blank"  -> FillBlankExerciseContent(
                            question = currentQuestion,
                            currentAnswer = tempAnswers[currentQuestion.questions_id]?.answer_text,
                            onAnswer = { answer ->
                                viewModel.saveStudentAnswer(currentQuestion.questions_id, answer)
                            }
                        )
                        "arrangement" -> ArrangementExerciseContent(
                            question = currentQuestion,
                            currentAnswer = tempAnswers[currentQuestion.questions_id]?.answer_text,
                            onAnswer = { answer ->
                                viewModel.saveStudentAnswer(currentQuestion.questions_id, answer)
                            }
                        )
                        "image_word" -> ImageWordExerciseContent(
                            question = currentQuestion,
                            currentAnswer = tempAnswers[currentQuestion.questions_id]?.answer_text,
                            onAnswer = { answer ->
                                viewModel.saveStudentAnswer(currentQuestion.questions_id, answer)
                            }
                        )
                        else -> Text("Loại câu hỏi không xác định: ${currentQuestion.question_type}")
                    }
                }
            }
        }
    }


    if (showSubmitDialog) {
        SubmitConfirmationDialog(
            answeredCount = tempAnswers.size,
            totalQuestions = questions.size,
            onConfirm = {
                submissionTime = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())
                viewModel.submitAllTempAnswers(
                    studentId = studentId,
                    courseId = courseId,
                    lessonPartId = lessonPartId,
                    onSuccess = { result ->
                        // Sau khi nộp bài thành công, chuyển sang màn hình kết quả
                        navController.navigate("result/$studentId/$courseId/$lessonPartId/$lessonPartTitle/$submissionTime") {
                            // Xóa tất cả các màn hình trên back stack cho đến màn hình LessonParts
                            // để khi nhấn back từ ResultScreen sẽ quay về LessonPartsScreen
                            popUpTo("lesson_parts/{courseId}") { inclusive = false }
                            launchSingleTop = true
                        }
                    },
                    onError = { error ->
                        // Xử lý lỗi nộp bài - hiển thị validation dialog
                        validationErrors = error.split("\n").filter { it.isNotBlank() }
                        showValidationDialog = true
                    }
                )
                showSubmitDialog = false // Đóng dialog sau khi xác nhận
            },
            onDismiss = { showSubmitDialog = false }
        )
    }

    // Validation Error Dialog
    if (showValidationDialog) {
        ValidationErrorDialog(
            errors = validationErrors,
            onDismiss = { showValidationDialog = false },
            onRetry = {
                // Có thể scroll đến câu hỏi đầu tiên có lỗi
                // hoặc highlight các câu hỏi có lỗi
            }
        )
    }
}

// Full Screen Loading
@Composable
fun FullScreenLoading() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        CircularProgressIndicator(color = Color(0xFF2196F3))
    }
}

// Error Screen
@Composable
fun ErrorScreen(
    message: String,
    onRetry: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = Icons.Default.Error,
            contentDescription = "Error",
            tint = Color.Red,
            modifier = Modifier.size(48.dp)
        )
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = message,
            style = MaterialTheme.typography.bodyLarge,
            color = Color.Red,
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.height(24.dp))
        Button(
            onClick = onRetry,
            colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF2196F3))
        ) {
            Text("Thử lại")
        }
    }
}

// Empty Question Screen
@Composable
fun EmptyQuestionScreen() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = Icons.Default.HourglassEmpty,
            contentDescription = "Empty",
            tint = Color.Gray,
            modifier = Modifier.size(48.dp)
        )
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = "Không có câu hỏi nào cho phần này",
            style = MaterialTheme.typography.bodyLarge,
            color = Color.Gray
        )
    }
}