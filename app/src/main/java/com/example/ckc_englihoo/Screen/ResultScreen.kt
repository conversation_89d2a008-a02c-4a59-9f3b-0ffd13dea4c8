package com.example.ckc_englihoo.Screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.DataClass.RecentSubmissionData
import com.example.ckc_englihoo.DataClass.StudentAnswerDetail

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ResultScreen(
    navController: NavController,
    studentId: Int,
    lessonPartTitle: String,
    courseId: Int,
    lessonPartId: Int,
    submissionTime: String,
    viewModel: AppViewModel
) {
    var resultData by remember { mutableStateOf<RecentSubmissionData?>(null) } // Thay đổi kiểu dữ liệu
    var isLoading by remember { mutableStateOf(true) }
    var errorMessage by remember { mutableStateOf("") }

    // Load result data when screen opens
    LaunchedEffect(Unit) {
        viewModel.getRecentSubmissionScoreAndProgress(
            studentId = studentId,
            courseId = courseId,
            lessonPartId = lessonPartId,
            submissionTime = submissionTime,
            onSuccess = { result ->
                resultData = result
                isLoading = false
            },
            onError = { error ->
                errorMessage = error
                isLoading = false
            }
        )
    }

    Scaffold(
        topBar = {
            CenterAlignedTopAppBar(
                title = {
                    Text(
                        text = "Kết quả học tập",
                        color = Color.White
                    )
                },
                navigationIcon = {
                    IconButton(onClick = {
                        navController.popBackStack("lesson_parts/{courseId}", false)
                    }) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                    }
                },
                colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                    containerColor = Color(0xFF2196F3),
                    titleContentColor = Color.White,
                    navigationIconContentColor = Color.White
                )
            )
        }
    ) { paddingValues ->
        when {
            isLoading -> {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(paddingValues),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(color = Color(0xFF2196F3))
                }
            }

            errorMessage.isNotEmpty() -> {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(paddingValues)
                        .padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = "Có lỗi xảy ra",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.Red
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = errorMessage,
                        fontSize = 14.sp,
                        color = Color.Gray,
                        textAlign = TextAlign.Center
                    )
                }
            }

            resultData != null -> Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    modifier = Modifier
                        .wrapContentWidth() // 👈 hoặc giữ nguyên fillMaxWidth nếu bạn muốn chiếm toàn chiều ngang
                        .padding(horizontal = 16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(24.dp)
                ) {
                    // Score Summary Card - Centered
                    Card(
                        modifier = Modifier.wrapContentWidth(), // 👈 thay vì fillMaxWidth()
                        colors = CardDefaults.cardColors(
                            containerColor = Color.White
                        ),
                        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
                    ) {
                        Column(
                            modifier = Modifier.padding(32.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = lessonPartTitle,
                                fontSize = 20.sp,
                                fontWeight = FontWeight.Medium,
                                textAlign = TextAlign.Center,
                                color = Color(0xFF0D47A1) // Màu xanh dương đậm
                            )

                            Spacer(modifier = Modifier.height(24.dp))

                            val score = resultData!!.score?.toFloat() ?: 0f
                            val totalQuestions = resultData!!.total_questions ?: 0
                            val correctAnswers = resultData!!.correct_answers ?: 0

                            Text(
                                text = "${score.toInt()}/10",
                                fontSize = 64.sp,
                                fontWeight = FontWeight.Bold,
                                textAlign = TextAlign.Center,
                                color = if (score >= 7) Color(0xFF4CAF50) else Color(0xFFF44336)
                            )

                            Spacer(modifier = Modifier.height(16.dp))

                            Text(
                                text = if (score >= 7) "Đạt" else "Chưa đạt",
                                fontSize = 20.sp,
                                fontWeight = FontWeight.Bold,
                                textAlign = TextAlign.Center,
                                color = if (score >= 7) Color(0xFF4CAF50) else Color(0xFFF44336)
                            )
                        }
                    }

                    // Confirm Button - Centered
                    Button(
                        onClick = {
                            navController.popBackStack("lesson_parts/{courseId}", false)
                        },
                        modifier = Modifier
                            .wrapContentWidth() // 👈 thay vì fillMaxWidth
                            .height(56.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF2196F3)
                        )
                    ) {
                        Text(
                            text = "Xác nhận",
                            color = Color.White,
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }

        }
    }
}

@Composable
fun QuestionResultCard(answerDetail: StudentAnswerDetail) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (answerDetail.is_correct) Color(0xFFE8F5E8) else Color(0xFFFFEBEE)
        )
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = if (answerDetail.is_correct) Icons.Default.Check else Icons.Default.Close,
                    contentDescription = null,
                    tint = if (answerDetail.is_correct) Color(0xFF4CAF50) else Color(0xFFF44336)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = answerDetail.question_text,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.weight(1f)
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "Câu trả lời của bạn: ${answerDetail.student_answer}",
                fontSize = 14.sp,
                color = if (answerDetail.is_correct) Color(0xFF2E7D32) else Color(0xFFC62828)
            )

            if (!answerDetail.is_correct) {
                Text(
                    text = "Đáp án đúng: ${answerDetail.correct_answer}",
                    fontSize = 14.sp,
                    color = Color(0xFF2E7D32),
                    modifier = Modifier.padding(top = 4.dp)
                )
            }

            answerDetail.feedback?.let { feedback ->
                if (feedback.isNotBlank()) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Giải thích: $feedback",
                        fontSize = 14.sp,
                        color = Color.Gray,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }
            }
        }
    }
}