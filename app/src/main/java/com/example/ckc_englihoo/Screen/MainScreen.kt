package com.example.ckc_englihoo.Screen

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import androidx.navigation.compose.rememberNavController
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.Navigation.*

@Composable
fun MainScreen(
    navRootController: NavHostController,
    viewModel: AppViewModel,
    modifier: Modifier = Modifier
) {
    val navItemController = rememberNavController()
    val currentStudent by viewModel.currentStudent.collectAsState()
    val currentTeacher by viewModel.currentTeacher.collectAsState()

    // Determine user role
    val isTeacher = currentTeacher != null
    val isStudent = currentStudent != null

    Scaffold(
        modifier = modifier.fillMaxSize(),
        bottomBar = {
            when {
                isTeacher -> NavigationAppBarTeacher(navController = navItemController)
                isStudent -> NavigationAppBarStudent(navController = navItemController)
            }
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier.padding(paddingValues)
        ) {
            when {
                isTeacher -> NavigationBarGraphTeacher(
                    NavItemController = navItemController,
                    NavRootController = navRootController,
                    viewModel = viewModel
                )
                isStudent -> NavigationBarGraphStudent(
                    NavItemController = navItemController,
                    NavRootController = navRootController,
                    viewModel = viewModel
                )
            }
        }
    }
}
