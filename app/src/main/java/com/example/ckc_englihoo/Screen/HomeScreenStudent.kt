package com.example.ckc_englihoo.Screen

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.ui.layout.ContentScale
// import coil.compose.AsyncImage // Tạm thời comment để tránh lỗi
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Chat
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.ExitToApp
import androidx.compose.material.icons.outlined.Home
import androidx.compose.material.icons.outlined.MenuBook
import androidx.compose.material.icons.outlined.Person
import androidx.compose.material.icons.filled.Assessment
import androidx.compose.foundation.clickable
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.DataClass.*
import com.example.ckc_englihoo.R
import androidx.compose.ui.text.style.TextAlign
import android.util.Log
import java.util.Date
import com.example.ckc_englihoo.Utils.StudentTopSection
import com.example.ckc_englihoo.Utils.ProgressSection

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreenStudent(
    navController: NavController,
    viewModel: AppViewModel
) {
    // Collect states from ViewModel
    val currentStudent by viewModel.currentStudent.collectAsState()
    val courses by viewModel.courses.collectAsState()
    val courseEnrollments by viewModel.courseEnrollments.collectAsState()
    val teachers by viewModel.teachers.collectAsState()
    val overallProgress by viewModel.overallProgress.collectAsState()
    val courseProgressMap by viewModel.courseProgressMap.collectAsState()
    val notifications by viewModel.notifications.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()

    // State để track việc load course progress
    var courseProgressLoaded by remember { mutableStateOf(false) }

    // Load basic data when student is available
    LaunchedEffect(currentStudent) {
        currentStudent?.let { student ->
            Log.d("HomeScreenStudent", "Loading basic data for student: ${student.fullname} (ID: ${student.student_id})")

            try {
                Log.d("HomeScreenStudent", "Loading courses, teachers, enrollments...")
                viewModel.loadAllCourses()
                viewModel.loadAllTeachers()
                viewModel.loadStudentEnrollments(student.student_id)

                // Load notifications with error handling
                try {
                    viewModel.loadStudentNotifications(student.student_id)
                } catch (e: Exception) {
                    Log.e("HomeScreenStudent", "Error loading notifications", e)
                    // Continue without notifications
                }
            } catch (e: Exception) {
                Log.e("HomeScreenStudent", "Error loading basic data", e)
            }
        }
    }

    // Load course progress after enrollments are loaded
    LaunchedEffect(courseEnrollments, currentStudent) {
        if (courseEnrollments.isNotEmpty() && currentStudent != null && !courseProgressLoaded) {
            Log.d("HomeScreenStudent", "Loading course progress for ${courseEnrollments.size} enrollments")

            // Lấy danh sách các khóa đang học (status = 2)
            val enrolledCourses = courseEnrollments.filter { it.status == 2 }
            val courseIds = enrolledCourses.map { it.assigned_course_id }

            Log.d("HomeScreenStudent", "Found ${enrolledCourses.size} enrolled courses: $courseIds")

            if (courseIds.isNotEmpty()) {
                // Load progress cho tất cả khóa học cùng lúc
                viewModel.loadMultipleCourseProgress(courseIds, currentStudent!!.student_id)
            }

            courseProgressLoaded = true
        }
    }

    // Process data for UI - chỉ hiển thị courses có enrollment status = 2 (đang học)
    val coursesForDisplay = remember(courses, courseEnrollments, teachers, courseProgressMap) {
        Log.d("HomeScreenStudent", "Processing data: ${courses.size} courses, ${courseEnrollments.size} enrollments, ${teachers.size} teachers")
        Log.d("HomeScreenStudent", "Course progress map: ${courseProgressMap.size} courses")
        val result = getCoursesForHomeDisplay(courses, courseEnrollments, teachers, courseProgressMap)
        Log.d("HomeScreenStudent", "Courses for display (only enrolled): ${result.size}")
        result
    }

    // Calculate average progress for enrolled courses only
    val averageProgress = remember(coursesForDisplay) {
        if (coursesForDisplay.isNotEmpty()) {
            coursesForDisplay.map { it.progress }.average()
        } else {
            0.0
        }
    }
    val notificationsForDisplay = remember(notifications) {
        getNotificationsForDisplay(notifications ?: emptyList())
    }
    val overallProgressPercentage = remember(overallProgress) {
        (overallProgress?.overall_progress_percentage ?: 0.0).toFloat() / 100f
    }

    Scaffold(
        topBar = {
            currentStudent?.let { student ->
                StudentTopSection(
                    student = student,
                    title = "Trang chủ",
                    onProfileClick = {
                        // Navigate to student profile screen
                        navController.navigate("student_profile")
                    },
                    onLogoutClick = {
                        viewModel.logout()
                        navController.navigate("login") {
                            popUpTo(0) { inclusive = true }
                        }
                    }
                )
            }
        },
        bottomBar = {  },
        containerColor = Color(0xFFE3F2FD) // Light Blue - xanh dương nhạt
    ) { paddingValues ->

        // Show loading state
        if (isLoading && currentStudent == null) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
            return@Scaffold
        }

        // Show error state
        errorMessage?.let { error ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "Có lỗi xảy ra",
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.colorScheme.error
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = error,
                    style = MaterialTheme.typography.bodyMedium,
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(16.dp))
                Button(
                    onClick = {
                        viewModel.clearErrorMessage()
                        currentStudent?.let { student ->
                            viewModel.refreshCurrentStudentData(student.student_id)
                        }
                    }
                ) {
                    Text("Thử lại")
                }
            }
            return@Scaffold
        }

        // Show empty state if no student
        if (currentStudent == null) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "Vui lòng đăng nhập để xem thông tin",
                    style = MaterialTheme.typography.bodyLarge
                )
            }
            return@Scaffold
        }

        // Main content
        Column(
            modifier = Modifier
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
                .fillMaxSize()
        ) {
            Spacer(Modifier.height(16.dp))

            // Progress Section - chỉ hiển thị tiến độ của các khóa đang học
            ProgressSection(
                overallProgress = averageProgress,
                completedCourses = coursesForDisplay.count { it.progress >= 1.0 },
                totalCourses = coursesForDisplay.size
            )

            Spacer(Modifier.height(16.dp))

            // Show loading indicator for content
            if (isLoading && courses.isEmpty()) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(60.dp),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(modifier = Modifier.size(24.dp))
                }
            }

            SectionTitle("Khóa đang học")
            CoursesRow(
                courses = coursesForDisplay,
                onCourseClick = { courseId ->
                    navController.navigate("lesson_parts/$courseId")
                }
            )
            Spacer(Modifier.height(16.dp))
            SectionTitle("Thông báo")
            NotificationsColumn(
                notifications = notificationsForDisplay,
                onViewAllClick = {
                    navController.navigate("notifications")
                }
            )
            Spacer(Modifier.height(16.dp))
        }
    }
}



@Composable
fun SectionTitle(title: String) {
    Text(
        text = title,
        style = MaterialTheme.typography.titleMedium,
        modifier = Modifier.padding(start = 16.dp, bottom = 8.dp)
    )
}

@Composable
fun CoursesRow(
    courses: List<CourseDisplayUI>,
    onCourseClick: (Int) -> Unit = {}
) {
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        contentPadding = PaddingValues(horizontal = 16.dp)
    ) {
        items(courses) { course ->
            ElevatedCard(
                modifier = Modifier
                    .width(200.dp)
                    .clickable { onCourseClick(course.courseId) },
                shape = RoundedCornerShape(12.dp)
            ) {
                Column(modifier = Modifier.padding(12.dp)) {
                    Text(course.courseName, style = MaterialTheme.typography.titleSmall)
                    Spacer(Modifier.height(4.dp))
                    Text(course.level, style = MaterialTheme.typography.bodySmall)
                    Spacer(Modifier.height(4.dp))
                    Text(
                        text = "GV: ${course.teacherName}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(Modifier.height(8.dp))
                    LinearProgressIndicator(
                        progress = { course.progress.toFloat() },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(6.dp)
                            .clip(RoundedCornerShape(3.dp))
                    )
                    Spacer(Modifier.height(4.dp))
                    Text(
                        text = "${(course.progress * 100).toInt()}%",
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
        }
    }
}

@Composable
fun NotificationsColumn(
    notifications: List<NotificationDisplayUI>,
    onViewAllClick: () -> Unit = {}
) {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(12.dp),
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
            .heightIn(max = 200.dp)
    ) {
        items(notifications) { note ->
            Card(
                shape = RoundedCornerShape(8.dp),
                colors = CardDefaults.elevatedCardColors(containerColor = Color(0xFFE6F3FF)), // Xanh dương nhạt hơn
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(modifier = Modifier.padding(12.dp)) {
                    Text(
                        text = note.title.takeIf { it.isNotEmpty() } ?: "Thông báo",
                        style = MaterialTheme.typography.titleSmall
                    )
                    Spacer(Modifier.height(2.dp))
                    Text(
                        text = note.message.takeIf { it.isNotEmpty() } ?: "Không có nội dung",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }

        // View all button
        item {
            TextButton(
                onClick = onViewAllClick,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = "Xem tất cả thông báo",
                    color = Color(0xFF4A90E2), // Xanh dương nhẹ nhàng hơn
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}


// Helper functions để xử lý dữ liệu từ ViewModel
// CHỈ HIỂN THỊ COURSES CÓ ENROLLMENT STATUS = 2 (ĐANG HỌC)
fun getCoursesForHomeDisplay(
    courses: List<Course>,
    courseEnrollments: List<CourseEnrollment>,
    teachers: List<Teacher>,
    courseProgressMap: Map<Int, CourseProgress>
): List<CourseDisplayUI> {
    // CHỈ LẤY CÁC ENROLLMENTS CÓ STATUS = 2 (ĐANG HỌC)
    return courseEnrollments.filter { it.status == 2 }.mapNotNull { enrollment ->
        val course = courses.find { it.course_id == enrollment.assigned_course_id }
        if (course != null) {
            // Tìm teacher cho course này - sử dụng course.teachers nếu có
            val teacher = course.teachers?.firstOrNull() ?: teachers.firstOrNull()

            // Get progress for this course from courseProgressMap
            val courseProgress = courseProgressMap[course.course_id]
            val progressValue = (courseProgress?.overall_progress_percentage ?: 0.0) / 100.0

            Log.d("HomeScreenStudent", "Course ${course.course_name} (ID: ${course.course_id}) progress: ${courseProgress?.overall_progress_percentage ?: 0.0}%")

            CourseDisplayUI(
                courseId = course.course_id,
                courseName = course.course_name,
                level = course.level,
                description = course.description,
                teacherName = (teacher as? TeacherWithPivot)?.fullname ?: "Chưa có giáo viên",
                progress = progressValue.toFloat(),
                status = "Đang học"
            )
        } else {
            null
        }
    }
}

fun getNotificationsForDisplay(notifications: List<Notification>): List<NotificationDisplayUI> {
    return try {
        notifications.take(5).mapNotNull { notification ->
            try {
                NotificationDisplayUI(
                    notificationId = notification.notification_id,
                    title = notification.title?.takeIf { it.isNotEmpty() } ?: "Thông báo",
                    message = notification.message?.takeIf { it.isNotEmpty() } ?: "",
                    date = when {
                        !notification.date.isNullOrEmpty() -> notification.date
                        !notification.notification_date.isNullOrEmpty() -> notification.notification_date
                        else -> ""
                    },
                    isRead = notification.isRead
                )
            } catch (e: Exception) {
                Log.e("HomeScreenStudent", "Error processing notification: ${notification.notification_id}", e)
                null
            }
        }
    } catch (e: Exception) {
        Log.e("HomeScreenStudent", "Error processing notifications list", e)
        emptyList()
    }
}

