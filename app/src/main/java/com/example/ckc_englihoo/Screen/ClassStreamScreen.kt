package com.example.ckc_englihoo.Screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.clickable
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Assessment
import androidx.compose.material.icons.filled.Comment
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Quiz
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Send
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.Button
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.AlertDialog
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.DataClass.*
import android.util.Log
import com.example.ckc_englihoo.Utils.formatTimeAgo
import java.util.Date



/**
 * ClassStreamScreen - STUDENT ONLY SCREEN
 * Features:
 * - View posts created by teachers (cannot create posts)
 * - View and add comments under posts
 * - Edit/delete only own comments (student_id based)
 * - Display actual names of post authors and commenters
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ClassStreamScreen(
    navController: NavController,
    viewModel: AppViewModel,
    courseId: Int = 1
) {
    // Collect states from ViewModel - STUDENT ONLY SCREEN
    val classPosts by viewModel.classPosts.collectAsState()
    val teachers by viewModel.teachers.collectAsState()
    val currentStudent by viewModel.currentStudent.collectAsState()
    val examResults by viewModel.examResults.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()
    val courses by viewModel.courses.collectAsState()

    // Note: This screen is student-only, no teacher functionality needed

    // Get course name from ViewModel
    val course = courses.find { it.course_id == courseId }
    val classTitle = course?.course_name ?: "Khóa học"

    // Load data when component mounts - sử dụng API mới
    LaunchedEffect(courseId) {
        Log.d("ClassStreamScreen", "Loading posts with comments for course $courseId")
        viewModel.loadPostsWithCommentsByCourse(courseId)
        viewModel.loadAllTeachers()
        currentStudent?.let { student ->
            viewModel.loadStudentExamResults(student.student_id)
        }
    }

    // Process class posts for display to ClassPostDisplayUI
    val allClassPosts = remember(classPosts, teachers) {
        getClassPostsWithCommentsForCourse(classPosts, teachers, courseId)
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = classTitle,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Quay lại"
                        )
                    }
                },
                actions = {
                    IconButton(onClick = { /* TODO: More options */ }) {
                        Icon(
                            imageVector = Icons.Default.MoreVert,
                            contentDescription = "Tùy chọn"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = MaterialTheme.colorScheme.onPrimary,
                    navigationIconContentColor = MaterialTheme.colorScheme.onPrimary,
                    actionIconContentColor = MaterialTheme.colorScheme.onPrimary
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp)
        ) {
            Spacer(modifier = Modifier.height(16.dp))

            // Khung input "Announce something to your class"
            AnnouncementInputCard()

            Spacer(modifier = Modifier.height(16.dp))

            // Show loading indicator for content
            if (isLoading) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(60.dp),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(modifier = Modifier.size(24.dp))
                }
            }

            // Danh sách bài đăng
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(12.dp),
                contentPadding = PaddingValues(bottom = 16.dp)
            ) {
                // Exam Results Section
                item {
                    ExamResultsSection(
                        examResults = examResults.filter { it.course_id == courseId },
                        onViewDetails = { examId ->
                            navController.navigate("exam_result_detail/$examId") {
                                launchSingleTop = true
                            }
                        }
                    )
                }

                items(allClassPosts) { post ->
                    ClassPostWithCommentsCard(
                        post = post,
                        viewModel = viewModel,
                        currentStudent = currentStudent
                        // Note: No currentTeacher needed - this is student-only screen
                    )
                }
            }
        }
    }
}

@Composable
fun AnnouncementInputCard() {
    var inputText by remember { mutableStateOf("") }

    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Row(
            modifier = Modifier.padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Person,
                contentDescription = "Avatar",
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape)
                    .background(MaterialTheme.colorScheme.primary),
                tint = MaterialTheme.colorScheme.onPrimary
            )
            Spacer(modifier = Modifier.width(12.dp))
            OutlinedTextField(
                value = inputText,
                onValueChange = { inputText = it },
                placeholder = {
                    Text(
                        "Thông báo gì đó cho lớp học của bạn...",
                        style = MaterialTheme.typography.bodyMedium
                    )
                },
                modifier = Modifier.weight(1f),
                shape = RoundedCornerShape(24.dp),
                trailingIcon = {
                    if (inputText.isNotEmpty()) {
                        IconButton(onClick = {
                            // TODO: Handle post submission
                            inputText = ""
                        }) {
                            Icon(
                                imageVector = Icons.Default.Add,
                                contentDescription = "Đăng bài",
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }
                    }
                }
            )
        }
    }
}

@Composable
fun ClassPostWithCommentsCard(
    post: ClassPostDisplayUI,
    viewModel: AppViewModel = androidx.lifecycle.viewmodel.compose.viewModel(),
    currentStudent: Student? = null,
    teachers: List<Teacher> = emptyList(),
    students: List<Student> = emptyList()
    // Note: currentTeacher removed - this component is for student-only screen
) {
    var showCommentInput by remember { mutableStateOf(false) }
    var commentText by remember { mutableStateOf("") }
    var editingComment by remember { mutableStateOf<ClassPostComment?>(null) }
    var showDeleteDialog by remember { mutableStateOf<ClassPostComment?>(null) }
    var showAllComments by remember { mutableStateOf(false) }
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            // Header với avatar và thông tin tác giả
            Row(verticalAlignment = Alignment.CenterVertically) {
                // Avatar tác giả
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(MaterialTheme.colorScheme.primary),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Person,
                        contentDescription = "Avatar",
                        tint = MaterialTheme.colorScheme.onPrimary,
                        modifier = Modifier.size(24.dp)
                    )
                }
                Spacer(modifier = Modifier.width(12.dp))
                Column {
                    Text(
                        text = post.teacherName,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.SemiBold,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    Text(
                        text = post.time,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // Nội dung bài đăng
            Text(
                text = post.content,
                style = MaterialTheme.typography.bodyMedium,
                lineHeight = 20.sp,
                color = MaterialTheme.colorScheme.onSurface
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Hiển thị comments nếu có
            post.comments?.let { comments ->
                if (comments.isNotEmpty()) {
                    Column {
                        Text(
                            text = "Bình luận (${comments.size})",
                            style = MaterialTheme.typography.labelMedium,
                            fontWeight = FontWeight.Medium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.padding(bottom = 8.dp)
                        )

                        val commentsToShow = if (showAllComments) comments else comments.take(3)

                        commentsToShow.forEach { comment ->
                            Log.d("ClassStreamScreen", "Rendering comment ${comment.comment_id}:")
                            Log.d("ClassStreamScreen", "  comment.student_id: ${comment.student_id}")
                            Log.d("ClassStreamScreen", "  comment.teacher_id: ${comment.teacher_id}")
                            Log.d("ClassStreamScreen", "  currentStudent?.student_id: ${currentStudent?.student_id}")

                            CommentItem(
                                comment = comment,
                                currentStudent = currentStudent,
                                teachers = teachers,
                                students = students,
                                // Note: currentTeacher removed - student-only screen
                                onEditComment = {
                                    editingComment = comment
                                    commentText = comment.content
                                    showCommentInput = true
                                },
                                onDeleteComment = { showDeleteDialog = comment }
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                        }

                        if (comments.size > 3) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 4.dp),
                                horizontalArrangement = Arrangement.Center
                            ) {
                                TextButton(
                                    onClick = { showAllComments = !showAllComments },
                                    modifier = Modifier.padding(horizontal = 8.dp)
                                ) {
                                    Icon(
                                        imageVector = if (showAllComments) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
                                        contentDescription = null,
                                        modifier = Modifier.size(16.dp)
                                    )
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Text(
                                        if (showAllComments) {
                                            "Ẩn bớt bình luận"
                                        } else {
                                            "Xem thêm ${comments.size - 3} bình luận"
                                        },
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.primary
                                    )
                                }
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(12.dp))
                }
            }

            // Khu vực thêm bình luận
            if (showCommentInput) {
                Column {
                    OutlinedTextField(
                        value = commentText,
                        onValueChange = { commentText = it },
                        label = {
                            Text(if (editingComment != null) "Chỉnh sửa bình luận" else "Thêm bình luận")
                        },
                        modifier = Modifier.fillMaxWidth(),
                        maxLines = 3
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Row(
                        horizontalArrangement = Arrangement.End,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        TextButton(
                            onClick = {
                                showCommentInput = false
                                commentText = ""
                                editingComment = null
                            }
                        ) {
                            Text("Hủy")
                        }

                        Spacer(modifier = Modifier.width(8.dp))

                        Button(
                            onClick = {
                                if (commentText.isNotBlank()) {
                                    if (editingComment != null) {
                                        // Update comment - only if user owns the comment
                                        if (currentStudent != null && editingComment!!.author_id == currentStudent.student_id) {
                                            Log.d("ClassStreamScreen", "Updating comment ${editingComment!!.comment_id} with content: '$commentText'")
                                            viewModel.updateClassPostComment(
                                                commentId = editingComment!!.comment_id,
                                                content = commentText
                                            )
                                        } else {
                                            Log.e("ClassStreamScreen", "Permission denied: Cannot update comment ${editingComment!!.comment_id}")
                                        }
                                    } else {
                                        // Create new comment (only students in ClassStreamScreen)
                                        viewModel.createClassPostComment(
                                            postId = post.classPostId,
                                            content = commentText,
                                            studentId = currentStudent?.student_id,
                                            teacherId = null // No teacher comments in student screen
                                        )
                                    }
                                    showCommentInput = false
                                    commentText = ""
                                    editingComment = null
                                }
                            },
                            enabled = commentText.isNotBlank()
                        ) {
                            Icon(
                                imageVector = Icons.Default.Send,
                                contentDescription = null,
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(if (editingComment != null) "Cập nhật" else "Gửi")
                        }
                    }
                }
            } else {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { showCommentInput = true }
                        .padding(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Comment,
                        contentDescription = "Icon bình luận",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "Thêm bình luận...",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }

    // Delete confirmation dialog
    showDeleteDialog?.let { comment ->
        AlertDialog(
            onDismissRequest = { showDeleteDialog = null },
            title = { Text("Xác nhận xóa") },
            text = { Text("Bạn có chắc chắn muốn xóa bình luận này không?") },
            confirmButton = {
                Button(
                    onClick = {
                        // Double-check permission before deleting
                        if (currentStudent != null && comment.author_id == currentStudent.student_id) {
                            Log.d("ClassStreamScreen", "Deleting comment ${comment.comment_id}")
                            viewModel.deleteClassPostComment(comment.comment_id)
                        } else {
                            Log.e("ClassStreamScreen", "Permission denied: Cannot delete comment ${comment.comment_id}")
                        }
                        showDeleteDialog = null
                    }
                ) {
                    Text("Xóa")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteDialog = null }
                ) {
                    Text("Hủy")
                }
            }
        )
    }
}

@Composable
fun CommentItem(
    comment: ClassPostComment,
    currentStudent: Student? = null,
    teachers: List<Teacher> = emptyList(),
    students: List<Student> = emptyList(),
    // Note: currentTeacher removed - this is for student-only screen
    onEditComment: () -> Unit = {},
    onDeleteComment: () -> Unit = {}
) {
    var showMenu by remember { mutableStateOf(false) }

    // Check if current user can edit/delete this comment (only students in ClassStreamScreen)
    val canModify = comment.author_id == currentStudent?.student_id

    // Debug logging for permission check
    Log.d("CommentItem", "Permission check for comment ${comment.comment_id}:")
    Log.d("CommentItem", "  currentStudent: ${currentStudent?.student_id}")
    Log.d("CommentItem", "  comment.student_id: ${comment.author_id}")
    Log.d("CommentItem", "  comment.teacher_id: ${comment.teacher_id}")
    Log.d("CommentItem", "  canModify: $canModify")

    // TEMPORARY: Show menu for all student comments for testing
    val showMenuForTesting = comment.author_id != null && currentStudent != null
    Log.d("CommentItem", "  showMenuForTesting: $showMenuForTesting")

    // Check if this is current user's comment for styling
    val isOwnComment = canModify
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(start = 8.dp)
            .then(
                if (isOwnComment) {
                    Modifier
                        .background(
                            MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f),
                            RoundedCornerShape(8.dp)
                        )
                        .padding(8.dp)
                } else {
                    Modifier
                }
            ),
        verticalAlignment = Alignment.Top
    ) {
        // Avatar comment author with different colors for student/teacher
        Box(
            modifier = Modifier
                .size(32.dp)
                .clip(CircleShape)
                .background(
                    when {
                        comment.student_id != null -> MaterialTheme.colorScheme.primaryContainer
                        comment.teacher_id != null -> MaterialTheme.colorScheme.tertiaryContainer
                        else -> MaterialTheme.colorScheme.secondaryContainer
                    }
                ),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = when {
                    !comment.author_name.isNullOrBlank() -> comment.author_name.firstOrNull()?.toString()?.uppercase() ?: "U"
                    comment.student_id != null -> "S"
                    comment.teacher_id != null -> "T"
                    else -> "U"
                },
                style = MaterialTheme.typography.bodySmall,
                fontWeight = FontWeight.Bold,
                color = when {
                    comment.student_id != null -> MaterialTheme.colorScheme.onPrimaryContainer
                    comment.teacher_id != null -> MaterialTheme.colorScheme.onTertiaryContainer
                    else -> MaterialTheme.colorScheme.onSecondaryContainer
                }
            )
        }

        Spacer(modifier = Modifier.width(8.dp))

        Column(modifier = Modifier.weight(1f)) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier.fillMaxWidth()
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        // Enhanced comment author name resolution
                        val authorName = remember(comment, teachers, students) {
                            Log.d("CommentItem", "🔍 RESOLVING comment ${comment.comment_id}:")
                            Log.d("CommentItem", "  📝 author_name='${comment.author_name}'")
                            Log.d("CommentItem", "  👨‍🏫 teacher_id=${comment.teacher_id}, 👨‍🎓 student_id=${comment.student_id}")
                            Log.d("CommentItem", "  🏷️ author_type='${comment.author_type}', 🆔 author_id=${comment.author_id}")
                            Log.d("CommentItem", "  📦 author object: ${comment.author}")
                            Log.d("CommentItem", "  📊 Available: teachers=${teachers.size}, students=${students.size}")

                            val result = when {
                                // 1. Try author object first (most reliable)
                                comment.author != null -> {
                                    try {
                                        when (comment.author) {
                                            is Map<*, *> -> {
                                                val authorMap = comment.author as Map<String, Any>
                                                val fullname = authorMap["fullname"] as? String
                                                if (!fullname.isNullOrBlank()) {
                                                    val cleanedName = fullname.replace("Không xác định", "").trim()
                                                    if (cleanedName.isNotBlank()) {
                                                        Log.d("CommentItem", "✅ Using author object fullname: '$cleanedName'")
                                                        cleanedName
                                                    } else {
                                                        Log.d("CommentItem", "❌ Author object fullname is just 'Không xác định'")
                                                        "Người dùng"
                                                    }
                                                } else {
                                                    Log.d("CommentItem", "❌ Author object has no fullname")
                                                    "Người dùng"
                                                }
                                            }
                                            else -> {
                                                Log.d("CommentItem", "❌ Author object is not a Map: ${comment.author::class.simpleName}")
                                                "Người dùng"
                                            }
                                        }
                                    } catch (e: Exception) {
                                        Log.d("CommentItem", "❌ Error parsing author object: ${e.message}")
                                        "Người dùng"
                                    }
                                }

                                // 2. Try author_name if available and clean
                                !comment.author_name.isNullOrBlank() -> {
                                    val cleanedName = comment.author_name.replace("Không xác định", "").trim()
                                    if (cleanedName.isNotBlank()) {
                                        Log.d("CommentItem", "✅ Using cleaned author_name: '$cleanedName'")
                                        cleanedName
                                    } else {
                                        Log.d("CommentItem", "❌ Author name is just 'Không xác định', trying other methods")
                                        "Người dùng"
                                    }
                                }

                                // 3. Try to find by student_id (this usually works)
                                comment.student_id != null -> {
                                    val student = students.find { it.student_id == comment.student_id }
                                    val name = student?.fullname ?: "Học sinh"
                                    Log.d("CommentItem", "✅ Student by student_id=${comment.student_id} -> '$name'")
                                    name
                                }

                                // 4. Try to find by teacher_id
                                comment.teacher_id != null -> {
                                    val teacher = teachers.find { it.teacher_id == comment.teacher_id }
                                    val name = teacher?.fullname ?: "Giáo viên"
                                    Log.d("CommentItem", "✅ Teacher by teacher_id=${comment.teacher_id} -> '$name'")
                                    name
                                }

                                // 5. Try author_type + author_id
                                comment.author_id > 0 && comment.author_type.isNotBlank() -> {
                                    when (comment.author_type.lowercase()) {
                                        "teacher" -> {
                                            val teacher = teachers.find { it.teacher_id == comment.author_id }
                                            val name = teacher?.fullname ?: "Giáo viên"
                                            Log.d("CommentItem", "✅ Teacher by author_id=${comment.author_id} -> '$name'")
                                            name
                                        }
                                        "student" -> {
                                            val student = students.find { it.student_id == comment.author_id }
                                            val name = student?.fullname ?: "Học sinh"
                                            Log.d("CommentItem", "✅ Student by author_id=${comment.author_id} -> '$name'")
                                            name
                                        }
                                        else -> {
                                            Log.d("CommentItem", "❌ Unknown author_type: '${comment.author_type}'")
                                            "Người dùng"
                                        }
                                    }
                                }

                                else -> {
                                    Log.d("CommentItem", "❌ No valid author info found")
                                    "Người dùng"
                                }
                            }

                            Log.d("CommentItem", "🎯 FINAL RESULT: '$result'")
                            result
                        }

                        Text(
                            text = authorName,
                            style = MaterialTheme.typography.bodySmall,
                            fontWeight = FontWeight.Medium,
                            color = MaterialTheme.colorScheme.onSurface
                        )

                        if (canModify) {
                            Text(
                                text = "Bạn",
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.primary,
                                modifier = Modifier
                                    .background(
                                        MaterialTheme.colorScheme.primaryContainer,
                                        RoundedCornerShape(4.dp)
                                    )
                                    .padding(horizontal = 4.dp, vertical = 1.dp)
                            )
                        }
                    }
                    Text(
                        text = formatTimeAgo(comment.created_at),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                // Menu button for edit/delete - ALWAYS SHOW FOR TESTING
                Log.d("CommentItem", "Showing menu for comment ${comment.comment_id}: canModify=$canModify, showMenuForTesting=$showMenuForTesting")
                // TEMPORARY: Always show menu for all comments to test functionality
                if (canModify) {
                    Box {
                        IconButton(
                            onClick = {
                                Log.d("CommentItem", "Menu button clicked for comment ${comment.comment_id}")
                                showMenu = true
                            },
                            modifier = Modifier
                                .size(32.dp)
                                .background(
                                    color = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                                    shape = CircleShape
                                )
                        ) {
                            Icon(
                                imageVector = Icons.Default.MoreVert,
                                contentDescription = "Menu",
                                modifier = Modifier.size(20.dp),
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }

                        DropdownMenu(
                            expanded = showMenu,
                            onDismissRequest = { showMenu = false }
                        ) {
                            DropdownMenuItem(
                                text = { Text("Chỉnh sửa") },
                                onClick = {
                                    showMenu = false
                                    onEditComment()
                                },
                                leadingIcon = {
                                    Icon(
                                        imageVector = Icons.Default.Edit,
                                        contentDescription = null,
                                        modifier = Modifier.size(16.dp)
                                    )
                                }
                            )
                            DropdownMenuItem(
                                text = { Text("Xóa") },
                                onClick = {
                                    showMenu = false
                                    onDeleteComment()
                                },
                                leadingIcon = {
                                    Icon(
                                        imageVector = Icons.Default.Delete,
                                        contentDescription = null,
                                        modifier = Modifier.size(16.dp)
                                    )
                                }
                            )
                        }
                    }
                }
            }

            Text(
                text = comment.content,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.padding(top = 2.dp)
            )
        }
    }
}

// Helper function để xử lý ClassPosts với Comments từ ViewModel data
fun getClassPostsWithCommentsForCourse(
    classPosts: List<ClassPost>,
    teachers: List<Teacher>,
    courseId: Int
): List<ClassPostDisplayUI> {
    Log.d("ClassStreamScreen", "Processing ${classPosts.size} posts for course $courseId")

    return classPosts.filter { it.course_id == courseId }.map { classPost ->
        // ClassPost only has teacher_id, so all posts are by teachers
        val authorName = classPost.teacher?.fullname ?: "Giáo viên"

        Log.d("ClassStreamScreen", "Post ${classPost.post_id}: ${classPost.comments?.size ?: 0} comments")

        ClassPostDisplayUI(
            classPostId = classPost.post_id,
            teacherName = authorName,
            teacherAvatar = null, // Simplified - no avatar for now
            time = formatTimeAgo(classPost.created_at),
            content = classPost.content,
            courseName = classPost.course?.course_name ?: "Khóa học",
            comments = classPost.comments // Thêm comments vào display UI
        )
    }.sortedByDescending { it.classPostId } // Sắp xếp theo thời gian mới nhất
}

@Composable
fun ExamResultsSection(
    examResults: List<ExamResult>,
    onViewDetails: (Int) -> Unit
) {
    if (examResults.isNotEmpty()) {
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
            colors = CardDefaults.cardColors(containerColor = Color(0xFFF3E5F5))
        ) {
            Column(modifier = Modifier.padding(16.dp)) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(
                        imageVector = Icons.Default.Assessment,
                        contentDescription = "Kết quả thi",
                        tint = Color(0xFF9C27B0),
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "Kết quả thi",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF9C27B0)
                    )
                }

                Spacer(modifier = Modifier.height(12.dp))

                examResults.take(3).forEach { result ->
                    ExamResultItem(
                        result = result,
                        onClick = { onViewDetails(result.exam_result_id) }
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }

                if (examResults.size > 3) {
                    TextButton(
                        onClick = { /* Navigate to full exam results */ },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("Xem tất cả kết quả")
                    }
                }
            }
        }
    }
}

@Composable
fun ExamResultItem(
    result: ExamResult,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = Icons.Default.Quiz,
            contentDescription = "Bài thi",
            tint = Color(0xFF9C27B0),
            modifier = Modifier.size(20.dp)
        )
        Spacer(modifier = Modifier.width(12.dp))
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = "Bài thi ${result.exam_result_id}",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = "Điểm: ${result.average_score}/10",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        Icon(
            imageVector = Icons.Default.KeyboardArrowRight,
            contentDescription = "Xem chi tiết",
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}