package com.example.ckc_englihoo.Screen.Exercises

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material.icons.filled.Send
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.DataClass.Answer
import com.example.ckc_englihoo.DataClass.Question
import com.example.ckc_englihoo.Utils.AnswerFeedback
import com.example.ckc_englihoo.Utils.Chip
import kotlin.collections.addAll
import kotlin.random.Random
import kotlin.toString

// Data class để lưu thông tin từ với màu sắc và vị trí
data class WordWithPosition(
    val answer: Answer,
    val color: Color,
    val originalIndex: Int,
    val positionId: String // Unique ID để phân biệt từ trùng
)

// Tạo danh sách màu sắc đẹp mắt
private fun generateBeautifulColors(count: Int): List<Color> {
    val beautifulColors = listOf(
        Color(0xFF6366F1), // Indigo
        Color(0xFF8B5CF6), // Violet
        Color(0xFFEC4899), // Pink
        Color(0xFFF59E0B), // Amber
        Color(0xFF10B981), // Emerald
        Color(0xFF3B82F6), // Blue
        Color(0xFFEF4444), // Red
        Color(0xFF84CC16), // Lime
        Color(0xFF06B6D4), // Cyan
        Color(0xFFF97316), // Orange
        Color(0xFF8B5A2B), // Brown
        Color(0xFF6B7280), // Gray
        Color(0xFF7C3AED), // Purple
        Color(0xFF059669), // Teal
        Color(0xFFDC2626), // Rose
    )

    return (0 until count).map {
        beautifulColors[it % beautifulColors.size]
    }
}


// Component Chip với màu sắc đẹp mắt và text canh giữa
@Composable
private fun BeautifulChip(
    text: String,
    color: Color,
    onClick: () -> Unit,
    showCloseIcon: Boolean = false
) {
    Surface(
        modifier = Modifier
            .padding(4.dp)
            .clickable(onClick = onClick),
        shape = RoundedCornerShape(20.dp),
        color = color,
        shadowElevation = 6.dp
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center, // Canh giữa nội dung
            modifier = Modifier
                .padding(horizontal = 16.dp, vertical = 12.dp)
                .defaultMinSize(minWidth = 60.dp) // Đảm bảo width tối thiểu
        ) {
            Text(
                text = text,
                color = Color.White,
                fontWeight = FontWeight.SemiBold,
                fontSize = 15.sp,
                textAlign = TextAlign.Center // Text canh giữa
            )
            if (showCloseIcon) {
                Spacer(modifier = Modifier.width(8.dp))
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "Remove",
                    tint = Color.White,
                    modifier = Modifier.size(18.dp)
                )
            }
        }
    }
}

@Composable
private fun SelectedWordsArea(
    words: List<String>,
    wordColors: Map<String, Color>,
    onWordRemoved: (String) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Câu trả lời của bạn:",
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold,
                color = Color(0xFF1976D2),
                modifier = Modifier.padding(bottom = 12.dp)
            )

            if (words.isEmpty()) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(60.dp)
                        .background(
                            Color(0xFFF5F5F5),
                            RoundedCornerShape(12.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "Nhấn vào từ bên dưới để thêm vào câu",
                        color = Color(0xFF757575),
                        style = MaterialTheme.typography.bodyMedium,
                        textAlign = TextAlign.Center
                    )
                }
            } else {
                // Tạo FlowRow để tự động xuống hàng
                val wordsInRows = remember(words) {
                    val rows = mutableListOf<List<String>>()
                    var currentRow = mutableListOf<String>()

                    words.forEach { word ->
                        // Kiểm tra nếu thêm từ này vào hàng hiện tại có vượt quá giới hạn không
                        if (currentRow.size >= 3) { // Tối đa 3 từ/hàng để đảm bảo không tràn
                            rows.add(currentRow.toList())
                            currentRow = mutableListOf(word)
                        } else {
                            currentRow.add(word)
                        }
                    }

                    // Thêm hàng cuối cùng nếu còn từ
                    if (currentRow.isNotEmpty()) {
                        rows.add(currentRow)
                    }

                    rows
                }

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = 200.dp)
                        .verticalScroll(rememberScrollState()),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    wordsInRows.forEach { rowWords ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .wrapContentHeight(),
                            horizontalArrangement = Arrangement.spacedBy(8.dp, Alignment.Start)
                        ) {
                            rowWords.forEach { word ->
                                BeautifulChip(
                                    text = word,
                                    color = wordColors[word] ?: Color(0xFF6366F1),
                                    onClick = { onWordRemoved(word) },
                                    showCloseIcon = true
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun AvailableWordsArea(
    allWords: List<String>,
    selectedWords: List<String>,
    wordColors: Map<String, Color>,
    onWordSelected: (String) -> Unit
) {
    // Tạo danh sách từ với position ID để xử lý từ trùng
    val availableWordsWithPosition = remember(allWords, selectedWords) {
        val selectedCount = mutableMapOf<String, Int>()
        selectedWords.forEach { word ->
            selectedCount[word] = selectedCount.getOrDefault(word, 0) + 1
        }

        val totalCount = mutableMapOf<String, Int>()
        allWords.forEach { word ->
            totalCount[word] = totalCount.getOrDefault(word, 0) + 1
        }

        allWords.mapIndexed { index, word ->
            val currentSelectedCount = selectedCount.getOrDefault(word, 0)
            val totalWordCount = totalCount.getOrDefault(word, 0)
            val isAvailable = currentSelectedCount < totalWordCount

            Triple(word, "${word}_$index", isAvailable)
        }.filter { it.third }
    }

    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Chọn từ để sắp xếp:",
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold,
                color = Color(0xFF1976D2),
                modifier = Modifier.padding(bottom = 12.dp)
            )

            LazyVerticalGrid(
                columns = GridCells.Fixed(3),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp),
                contentPadding = PaddingValues(8.dp),
                modifier = Modifier.heightIn(max = 300.dp) // Giới hạn chiều cao và cho phép cuộn
            ) {
                items(availableWordsWithPosition) { (word, positionId, _) ->
                    BeautifulChip(
                        text = word,
                        color = wordColors[word] ?: Color(0xFF6366F1),
                        onClick = { onWordSelected(word) },
                        showCloseIcon = false
                    )
                }
            }
        }
    }
}

@Composable
fun ArrangementExerciseContent(
    question: Question,
    currentAnswer: String?,
    onAnswer: (String) -> Unit
) {
    // State holds selected answers - GIỮ NGUYÊN LOGIC GỐC
    val selectedWords = remember {
        mutableStateListOf<Answer>().apply {
            currentAnswer?.split(",")?.mapNotNull { idString ->
                idString.toIntOrNull()?.let { id ->
                    question.answers.find { it.answers_id == id }
                }
            }?.let { addAll(it) }
        }
    }

    // Emit answer as needed - GIỮ NGUYÊN LOGIC GỐC
    fun emitAnswer() {
        val result = selectedWords.joinToString(",") { it.answers_id.toString() }
        onAnswer(result)
    }

    // Tạo màu sắc đẹp cho từng từ
    val wordColors = remember(question.answers) {
        val colors = generateBeautifulColors(question.answers.size)
        question.answers.mapIndexed { index, answer ->
            answer.answer_text to colors[index]
        }.toMap()
    }

    // Background gradient đẹp mắt
    val backgroundGradient = Brush.verticalGradient(
        colors = listOf(
            Color(0xFFE3F2FD), // Light blue
            Color(0xFFF3E5F5)  // Light purple
        )
    )

    Column(
        modifier = Modifier
            .height(610.dp)
            .fillMaxWidth()
            .background(backgroundGradient)
            .padding(16.dp)
    ) {
        // Question Card với gradient đẹp
        Card(
            shape = RoundedCornerShape(20.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
            modifier = Modifier.fillMaxWidth()
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        Color.White,
                        RoundedCornerShape(20.dp)
                    )
                    .border(
                        2.dp,
                        Color(0xFF1976D2),
                        RoundedCornerShape(20.dp)
                    )
                    .padding(24.dp)
            ) {
                Text(
                    text = question.question_text,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF1976D2),
                    lineHeight = 26.sp,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }

        Spacer(Modifier.height(16.dp))

        // Selected area - GIỮ NGUYÊN LOGIC GỐC
        SelectedWordsArea(
            words = selectedWords.map { it.answer_text },
            wordColors = wordColors,
            onWordRemoved = { wordText ->
                selectedWords.removeAll { it.answer_text == wordText }
                emitAnswer()
            }
        )

        Spacer(Modifier.height(16.dp))

        // Available area - GIỮ NGUYÊN LOGIC GỐC
        AvailableWordsArea(
            allWords = question.answers.shuffled().map { it.answer_text },
            selectedWords = selectedWords.map { it.answer_text },
            wordColors = wordColors,
            onWordSelected = { wordText ->
                question.answers.find { it.answer_text == wordText }?.let {
                    selectedWords.add(it)
                    emitAnswer()
                }
            }
        )

        Spacer(Modifier.height(24.dp))
    }
}
