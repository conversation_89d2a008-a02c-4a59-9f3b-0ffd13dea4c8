package com.example.ckc_englihoo.Screen.Exercises

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import com.example.ckc_englihoo.DataClass.Question
import com.example.ckc_englihoo.R
import kotlin.text.clear

@Composable
fun SelectedLettersRow(
    letters: List<String>,
    onLetterRemoved: (Int) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 8.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        letters.forEachIndexed { idx, letter ->
            LetterBox(letter = letter) { onLetterRemoved(idx) }
        }
    }
}

@Composable
fun LetterKeyboard(
    letters: List<String>,
    enabledLetters: List<String>,
    onLetterPressed: (String) -> Unit
) {
    LazyVerticalGrid(
        columns = GridCells.Fixed(4),
        contentPadding = PaddingValues(8.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        modifier = Modifier.height(240.dp)
    ) {
        items(letters) { letter ->
            val enabled = enabledLetters.contains(letter)
            LetterButton(
                letter = letter,
                enabled = enabled,
                onClick = { if (enabled) onLetterPressed(letter) }
            )
        }
    }
}

@Composable
fun LetterBox(
    letter: String,
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier
            .size(48.dp)
            .clickable(onClick = onClick),
        shape = RoundedCornerShape(8.dp),
        color = Color(0xFFFFD600),
        border = BorderStroke(1.dp, Color.Gray)
    ) {
        Box(Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            Text(
                text = letter,
                fontSize = 24.sp,
                color = Color.Black
            )
        }
    }
}


@Composable
fun LetterButton(
    letter: String,
    enabled: Boolean,
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier
            .padding(4.dp)
            .aspectRatio(1f)
            .clickable(enabled = enabled, onClick = onClick),
        shape = RoundedCornerShape(8.dp),
        color = if (enabled) Color(0xFF2196F3) else Color.LightGray
    ) {
        Box(Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            androidx.compose.material3.Text(
                text = letter,
                fontSize = 24.sp,
                color = if (enabled) Color.White else Color.DarkGray
            )
        }
    }
}

@Composable
fun ImageWordExerciseContent(
    question: Question,
    currentAnswer: String?,
    onAnswer: (String) -> Unit
) {
    val context = LocalContext.current
    // Lấy các ký tự đúng (is_correct == 1) theo thứ tự order_index
    val correctChars = question.answers
        .filter { it.is_correct == 1 }
        .sortedBy { it.order_index }
        .map { it.answer_text }

    // Độ dài câu trả lời cần điền
    val slotCount = correctChars.size

    // Tất cả letters (đúng + sai) để bấm
    val allLetters = remember(question.answers) {
        question.answers.map { it.answer_text }.shuffled()
    }

    // State: danh sách slot, null = chưa điền
    val slots = remember { mutableStateListOf<String?>().apply {
        repeat(slotCount) { add(null) }
    } }

    // Khi currentAnswer thay đổi (từ viewModel), khôi phục slots
    LaunchedEffect(currentAnswer) {
        slots.clear()
        val chars = currentAnswer?.toList()?.map { it.toString() } ?: emptyList()
        // điền lại theo thứ tự:
        repeat(slotCount) { idx ->
            slots.add(chars.getOrNull(idx))
        }
    }

    // Gửi answer (concatenate non-null)
    fun emit() {
        onAnswer(slots.filterNotNull().joinToString(""))
    }

    Column(
        Modifier
            .height(610.dp)
            .fillMaxWidth()
            .background(Color(0xFFE0F7FA))
            .verticalScroll(rememberScrollState())
            .padding(16.dp)
    ) {
        // 1. Hiển thị image (giống cũ)
        val correctAnswerItem = question.answers
            .firstOrNull {
                it.is_correct == 1
                        && !it.media_url.isNullOrBlank()
            }
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .height(200.dp),
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.cardColors(containerColor = Color.LightGray)
        ) {
            Box(Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {

                val mediaUrl = question?.media_url
                if (!mediaUrl.isNullOrBlank()) {
                    AsyncImage(
                        model = mediaUrl,
                        contentDescription = null,
                        modifier = Modifier
                            .fillMaxSize()
                            .clip(RoundedCornerShape(12.dp)),
                        contentScale = ContentScale.Crop,
                        placeholder = painterResource(R.drawable.placeholder)
                    )
                } else {
                    Image(
                        painter = painterResource(R.drawable.placeholder),
                        contentDescription = null,
                        modifier = Modifier
                            .fillMaxSize()
                            .clip(RoundedCornerShape(12.dp)),
                        contentScale = ContentScale.Crop
                    )
                }
            }
        }

        Spacer(Modifier.height(24.dp))

        // 2. Row các ô slot cố định
        LazyRow(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp, alignment = Alignment.CenterHorizontally),
            verticalAlignment = Alignment.CenterVertically
        ) {
            items(slots.size) { idx ->
                val letter = slots[idx]
                Box(
                    modifier = Modifier
                        .size(48.dp)
                        .border(1.dp, Color.Gray, RoundedCornerShape(8.dp))
                        .clickable(enabled = letter != null) {
                            slots[idx] = null
                            emit()
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = letter ?: "",
                        fontSize = 24.sp,
                        color = Color.Black
                    )
                }
            }
        }

        Spacer(Modifier.height(24.dp))

        // 3. Bàn phím letters
        val used = slots.filterNotNull()
        LazyVerticalGrid(
            columns = GridCells.Fixed(4),
            modifier = Modifier
                .fillMaxWidth()
                .height(240.dp),
            contentPadding = PaddingValues(8.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(allLetters) { letter ->
                val enabled = !used.contains(letter) && slots.any { it == null }
                Box(
                    modifier = Modifier
                        .size(56.dp)
                        .clip(RoundedCornerShape(8.dp))
                        .background(if (enabled) Color(0xFF2196F3) else Color.LightGray)
                        .clickable(enabled = enabled) {
                            // điền vào slot đầu tiên null
                            val firstNull = slots.indexOfFirst { it == null }
                            if (firstNull != -1) {
                                slots[firstNull] = letter
                                emit()
                            }
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = letter,
                        fontSize = 24.sp,
                        color = if (enabled) Color.White else Color.DarkGray
                    )
                }
            }
        }
    }
}