package com.example.ckc_englihoo.Screen

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.DataClass.*
import android.util.Log

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LessonPartsScreen(
    navController: NavController,
    viewModel: AppViewModel,
    courseId: Int
) {
    // Collect states from ViewModel
    val lessonPartsWithProgress by viewModel.lessonPartsWithProgress.collectAsState()
    val courseProgress by viewModel.courseProgress.collectAsState()
    val currentStudent by viewModel.currentStudent.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()
    val courses by viewModel.courses.collectAsState()

    // Get course name from ViewModel
    val course = courses.find { it.course_id == courseId }
    val courseName = course?.course_name ?: "Khóa học"

    // Load course progress when screen loads
    LaunchedEffect(courseId, currentStudent) {
        currentStudent?.let { student ->
            Log.d("LessonPartsScreen", "Loading course progress for course $courseId, student ${student.student_id}")
            viewModel.loadCourseProgress(courseId, student.student_id)
            // Also load lesson parts for the course
            viewModel.loadLessonPartsByCourse(courseId)
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Column {
                        Text(
                            text = courseName,
                            color = Color.White,
                            fontWeight = FontWeight.Bold,
                            fontSize = 18.sp
                        )
                        Text(
                            text = "Chọn bài học",
                            color = Color.White.copy(alpha = 0.8f),
                            fontSize = 14.sp
                        )
                    }
                },
                navigationIcon = {
                    IconButton(
                        onClick = { navController.popBackStack() },
                        modifier = Modifier
                            .size(48.dp)
                            .clip(CircleShape)
                    ) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Quay lại",
                            tint = Color.White,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color(0xFF1976D2) // Xanh dương đậm
                )
            )
        },
        containerColor = Color(0xFFE3F2FD) // Light Blue - xanh dương nhạt
    ) { paddingValues ->

        // Show loading state
        if (isLoading && lessonPartsWithProgress.isEmpty()) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(color = Color(0xFF1976D2))
            }
            return@Scaffold
        }

        // Show error state
        errorMessage?.let { error ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "Có lỗi xảy ra",
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.colorScheme.error
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = error,
                    style = MaterialTheme.typography.bodyMedium,
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(16.dp))
                Button(
                    onClick = {
                        viewModel.clearErrorMessage()
                        currentStudent?.let { student ->
                            viewModel.loadCourseProgress(courseId, student.student_id)
                            viewModel.loadLessonPartsByCourse(courseId)
                        }
                    }
                ) {
                    Text("Thử lại")
                }
            }
            return@Scaffold
        }

        // Main content
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // Create lesson parts with progress from courseProgress data
            val lessonPartsWithProgressFromCourse = remember(courseProgress) {
                courseProgress?.lessons_progress?.map { lessonProgressItem ->
                    LessonPartWithProgress(
                        lesson_part_id = lessonProgressItem.lesson_part_id,
                        level = lessonProgressItem.level,
                        part_type = lessonProgressItem.lesson_title,
                        partType = lessonProgressItem.lesson_title,
                        type = lessonProgressItem.lesson_title,
                        content = "",
                        description = lessonProgressItem.lesson_title,
                        progress = (lessonProgressItem.progress_percentage / 100.0).toFloat(),
                        order_index = 0,
                        total_questions = lessonProgressItem.total_questions,
                        is_completed = lessonProgressItem.is_completed,
                        progress_percentage = lessonProgressItem.progress_percentage,
                        best_score = null,
                        last_attempt_date = null,
                        created_at = "",
                        updated_at = "",
                        lesson = null
                    )
                } ?: emptyList()
            }

            // Header info
            LessonPartsHeader(courseName, lessonPartsWithProgressFromCourse.size)

            // Show course progress overview
            courseProgress?.let { progress ->
                CourseProgressOverview(progress)
            }

            // Lesson parts list
            if (lessonPartsWithProgressFromCourse.isNotEmpty()) {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(16.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    items(lessonPartsWithProgressFromCourse) { lessonPartWithProgress ->
                        LessonPartWithProgressCard(
                            lessonPart = lessonPartWithProgress,
                            onClick = { lessonPartId ->
                                currentStudent?.let { student ->
                                    // Navigate to questions with proper route including courseId
                                    navController.navigate(
                                        "questions/$lessonPartId/${lessonPartWithProgress.part_type}/${student.student_id}/$courseId"
                                    ) {
                                        // Ensure proper back stack management
                                        launchSingleTop = true
                                    }
                                }
                            }
                        )
                    }
                }
            } else if (courseProgress == null && !isLoading) {
                // Empty state - no course progress data
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Filled.MenuBook,
                            contentDescription = null,
                            modifier = Modifier.size(64.dp),
                            tint = Color.Gray
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "Chưa có dữ liệu tiến trình",
                            style = MaterialTheme.typography.titleMedium,
                            color = Color.Gray
                        )
                        Text(
                            text = "Vui lòng thử lại sau",
                            style = MaterialTheme.typography.bodyMedium,
                            color = Color.Gray
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun CourseProgressOverview(courseProgress: CourseProgress) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF1976D2).copy(alpha = 0.1f)
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Tiến trình khóa học",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF1976D2)
            )

            Spacer(modifier = Modifier.height(12.dp))

            // Progress bar
            LinearProgressIndicator(
                progress = { (courseProgress.overall_progress_percentage / 100.0).toFloat() },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(8.dp)
                    .clip(RoundedCornerShape(4.dp)),
                color = Color(0xFF1976D2),
                trackColor = Color(0xFF1976D2).copy(alpha = 0.2f)
            )

            Spacer(modifier = Modifier.height(8.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "${String.format("%.1f", courseProgress.overall_progress_percentage)}% hoàn thành",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFF1976D2),
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = "${courseProgress.answered_questions}/${courseProgress.total_questions} câu hỏi",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.Gray
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Only show accuracy percentage
            Text(
                text = "Độ chính xác: ${String.format("%.1f", courseProgress.correct_percentage)}%",
                style = MaterialTheme.typography.bodySmall,
                color = if (courseProgress.correct_percentage >= 70) Color(0xFF4CAF50) else Color(0xFFFF9800)
            )
        }
    }
}

@Composable
fun LessonPartsHeader(courseName: String, totalParts: Int) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Filled.School,
                    contentDescription = null,
                    tint = Color(0xFF1976D2),
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(12.dp))
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = courseName,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF111827)
                    )
                    Text(
                        text = "$totalParts bài học có sẵn",
                        fontSize = 14.sp,
                        color = Color(0xFF6B7280)
                    )
                }
            }
        }
    }
}

@Composable
fun LessonPartWithProgressCard(
    lessonPart: LessonPartWithProgress,
    onClick: (Int) -> Unit
) {
    val partIcon = getLessonPartIcon(lessonPart.part_type)
    val partColor = getLessonPartColor(lessonPart.part_type)

    ElevatedCard(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick(lessonPart.lesson_part_id) },
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.elevatedCardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.elevatedCardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Icon section with completion status
                Box(
                    modifier = Modifier
                        .size(56.dp)
                        .background(
                            color = if (lessonPart.is_completed) Color(0xFF10B981).copy(alpha = 0.1f) else partColor.copy(alpha = 0.1f),
                            shape = RoundedCornerShape(12.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    if (lessonPart.is_completed) {
                        Icon(
                            imageVector = Icons.Filled.CheckCircle,
                            contentDescription = "Hoàn thành",
                            tint = Color(0xFF10B981),
                            modifier = Modifier.size(28.dp)
                        )
                    } else {
                        Icon(
                            imageVector = partIcon,
                            contentDescription = lessonPart.part_type,
                            tint = partColor,
                            modifier = Modifier.size(28.dp)
                        )
                    }
                }

                Spacer(modifier = Modifier.width(16.dp))

                // Content section
                Column(modifier = Modifier.weight(1f)) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = lessonPart.part_type,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF111827)
                        )
                        if (lessonPart.is_completed) {
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "✓",
                                fontSize = 14.sp,
                                color = Color(0xFF10B981),
                                fontWeight = FontWeight.Bold
                            )
                        }
                    }
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = lessonPart.content.take(80) + if (lessonPart.content.length > 80) "..." else "",
                        fontSize = 14.sp,
                        color = Color(0xFF6B7280),
                        lineHeight = 20.sp
                    )
                }

                // Arrow icon
                Icon(
                    imageVector = Icons.Filled.ChevronRight,
                    contentDescription = "Vào bài học",
                    tint = Color(0xFF9CA3AF),
                    modifier = Modifier.size(24.dp)
                )
            }

            // Progress section
            Spacer(modifier = Modifier.height(16.dp))

            // Progress bar
            LinearProgressIndicator(
                progress = (lessonPart.progress_percentage / 100).toFloat(),
                modifier = Modifier
                    .fillMaxWidth()
                    .height(6.dp)
                    .clip(RoundedCornerShape(3.dp)),
                color = if (lessonPart.is_completed) Color(0xFF10B981) else partColor
            )

            Spacer(modifier = Modifier.height(8.dp))

            // Progress info
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "${String.format("%.1f", lessonPart.progress_percentage)}% hoàn thành",
                    fontSize = 12.sp,
                    color = Color(0xFF6B7280)
                )
                Text(
                    text = "${lessonPart.total_questions} câu hỏi",
                    fontSize = 12.sp,
                    color = Color(0xFF6B7280)
                )
            }

            // Best score if available
            lessonPart.best_score?.let { score ->
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "Điểm cao nhất: ${String.format("%.1f", score)}/10",
                    fontSize = 12.sp,
                    color = partColor,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

// Helper functions
fun getLessonPartIcon(partType: String): ImageVector {
    return when (partType.lowercase()) {
        "vocabulary" -> Icons.Filled.Spellcheck
        "grammar" -> Icons.Filled.Edit
        "listening" -> Icons.Filled.Hearing
        "speaking" -> Icons.Filled.RecordVoiceOver
        "reading" -> Icons.Filled.MenuBook
        "writing" -> Icons.Filled.Create
        "pronunciation" -> Icons.Filled.GraphicEq
        "practice test" -> Icons.Filled.Quiz
        else -> Icons.Filled.Assignment
    }
}

fun getLessonPartColor(partType: String): Color {
    return when (partType.lowercase()) {
        "vocabulary" -> Color(0xFF10B981) // Green
        "grammar" -> Color(0xFF3B82F6) // Blue
        "listening" -> Color(0xFF8B5CF6) // Purple
        "speaking" -> Color(0xFFEF4444) // Red
        "reading" -> Color(0xFFF59E0B) // Yellow
        "writing" -> Color(0xFF06B6D4) // Cyan
        "pronunciation" -> Color(0xFFEC4899) // Pink
        "practice test" -> Color(0xFF6366F1) // Indigo
        else -> Color(0xFF6B7280) // Gray
    }
}

// Convert LessonPart to LessonPartWithProgress
fun convertToLessonPartWithProgress(lessonPart: LessonPart): LessonPartWithProgress {
    return try {
        LessonPartWithProgress(
            lesson_part_id = lessonPart.lesson_part_id,
            level = lessonPart.level?.takeIf { it.isNotEmpty() } ?: "",
            part_type = lessonPart.part_type?.takeIf { it.isNotEmpty() } ?: "General",
            partType = lessonPart.partType?.takeIf { it.isNotEmpty() }
                ?: lessonPart.part_type?.takeIf { it.isNotEmpty() }
                ?: "General",
            type = lessonPart.type?.takeIf { it.isNotEmpty() }
                ?: lessonPart.part_type?.takeIf { it.isNotEmpty() }
                ?: "General",
            content = lessonPart.content?.takeIf { it.isNotEmpty() } ?: "",
            description = lessonPart.description?.takeIf { it.isNotEmpty() } ?: "",
            progress = lessonPart.progress ?: 0.0f,
            order_index = lessonPart.order_index ?: 0,
            total_questions = 10, // Default value
            is_completed = false, // Default value
            progress_percentage = (lessonPart.progress ?: 0.0f).toDouble(),
            best_score = null,
            last_attempt_date = null,
            created_at = lessonPart.created_at?.takeIf { it.isNotEmpty() } ?: "",
            updated_at = lessonPart.updated_at?.takeIf { it.isNotEmpty() } ?: "",
            lesson = lessonPart.lesson
        )
    } catch (e: Exception) {
        Log.e("LessonPartsScreen", "Error converting LessonPart to LessonPartWithProgress: ${lessonPart.lesson_part_id}", e)
        // Return a safe default
        LessonPartWithProgress(
            lesson_part_id = lessonPart.lesson_part_id ?: 0,
            level = "",
            part_type = "General",
            partType = "General",
            type = "General",
            content = "",
            description = "",
            progress = 0.0f,
            order_index = 0,
            total_questions = 0,
            is_completed = false,
            progress_percentage = 0.0,
            best_score = null,
            last_attempt_date = null,
            created_at = "",
            updated_at = "",
            lesson = null
        )
    }
}
