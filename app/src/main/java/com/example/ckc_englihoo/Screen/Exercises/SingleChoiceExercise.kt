package com.example.ckc_englihoo.Screen.Exercises

import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.ckc_englihoo.DataClass.Question
import kotlin.text.chunked
import kotlin.text.forEachIndexed


@Composable
fun SingleChoiceExerciseContent(
    question: Question,
    currentAnswer: String?, // Gi<PERSON> trị đáp án đã lưu (answer_id)
    onAnswer: (String) -> Unit,
) {
    val options = question.answers.map { it.answer_text }
    val labels = listOf("A", "B", "C", "D")

    // Chuyển đổi currentAnswer (answer_id) thành answer_text để hiển thị
    val currentAnswerText = currentAnswer?.toIntOrNull()?.let { answerId ->
        question.answers.find { it.answers_id == answerId }?.answer_text
    }

    // Sử dụng currentAnswerText để khởi tạo selectedOption
    var selectedOption by remember(currentAnswerText) { mutableStateOf(currentAnswerText) }

    Column(
        modifier = Modifier
            .height(610.dp)
            .fillMaxWidth()
            .background(Color(0xFFE0F7FA))
            .padding(16.dp)
    ) {
        // 1. Phần Question
        Card(
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
            modifier = Modifier
                .fillMaxWidth()
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.padding(vertical = 24.dp, horizontal = 16.dp)
            ) {

                Spacer(modifier = Modifier.height(16.dp))

                // Text câu hỏi
                Text(
                    text = question.question_text,
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF0D47A1),
                    lineHeight = 28.sp,
                    modifier = Modifier.fillMaxWidth(),
                    textAlign = TextAlign.Center
                )
            }
        }

        Spacer(modifier = Modifier.height(32.dp))

        // 2. Phần Answer Options: 2 cột
        Column(modifier = Modifier.fillMaxWidth()) {
            options.chunked(2).forEachIndexed { rowIndex, rowItems ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    rowItems.forEachIndexed { index, option ->
                        val globalIndex = rowIndex * 2 + index
                        val label = labels.getOrNull(globalIndex) ?: ""
                        val isSelected = selectedOption == option // So sánh với selectedOption
                        val borderColor = if (isSelected) Color(0xFF64B5F6) else Color(0xFFB0BEC5)

                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .height(64.dp)
                                .clip(RoundedCornerShape(12.dp))
                                .background(Color.White)
                                .border(2.dp, borderColor, RoundedCornerShape(12.dp))
                                .clickable {
                                    selectedOption = option // Cập nhật selectedOption
                                    // Tìm answer_id từ answer_text và gửi lên ViewModel
                                    val answerId = question.answers.find { it.answer_text == option }?.answers_id
                                    onAnswer(answerId?.toString() ?: option) // Gửi answer_id thay vì answer_text
                                }
                                .padding(start = 16.dp, end = 12.dp),
                            contentAlignment = Alignment.CenterStart
                        ) {
                            Row(verticalAlignment = Alignment.CenterVertically) {
                                // Vòng tròn label
                                Box(
                                    contentAlignment = Alignment.Center,
                                    modifier = Modifier
                                        .size(28.dp)
                                        .background(
                                            if (isSelected) Color(0xFF64B5F6) else Color(0xFFB0BEC5),
                                            shape = CircleShape
                                        )
                                ) {
                                    Text(
                                        text = label,
                                        color = Color.White,
                                        fontWeight = FontWeight.Bold
                                    )
                                }
                                Spacer(modifier = Modifier.width(12.dp))
                                // Nội dung đáp án
                                Text(
                                    text = option,
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = if (isSelected) Color(0xFF0D47A1) else Color(0xFF2196F3)
                                )
                            }
                        }
                    }
                    // Nếu số đáp án lẻ, thêm Spacer để đều khoảng trống
                    if (rowItems.size == 1) {
                        Spacer(modifier = Modifier.weight(1f))
                    }
                }
                Spacer(modifier = Modifier.height(16.dp))
            }
        }
    }
}