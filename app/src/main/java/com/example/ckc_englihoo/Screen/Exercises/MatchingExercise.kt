package com.example.ckc_englihoo.Screen.Exercises

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import com.example.ckc_englihoo.DataClass.Answer
import com.example.ckc_englihoo.DataClass.Question
import com.example.ckc_englihoo.R
import kotlin.collections.toMap

@Composable
fun MatchingExerciseContent(
    question: Question,
    currentAnswer: String?,
    onAnswer: (String) -> Unit
) {
    val context = LocalContext.current

    val boxBackgroundColor = Color(0xFF0288D1)
    val cardBorderColor = Color(0xFF81D4FA)
    val selectedButtonColor = Color(0xFF0288D1)

    var selectedLeft by remember { mutableStateOf<Answer?>(null) }
    var hasUserInteracted by remember { mutableStateOf(false) }

    // Khôi phục matchedPairs từ currentAnswer
    val matchedPairs = remember {
        mutableStateMapOf<Int, Int>().apply {
            currentAnswer?.split(",")?.forEach { pairString ->
                val parts = pairString.split(":")
                if (parts.size == 2) {
                    val key = parts[0].toIntOrNull()
                    val value = parts[1].toIntOrNull()
                    if (key != null && value != null) {
                        put(key, value)
                    }
                }
            }
        }
    }
    Column(
        modifier = Modifier
            .height(610.dp)
            .fillMaxWidth()
            .background(Color(0xFFE0F7FA))
            .padding(16.dp)
    ) {
        Text("Chọn từ trước:", style = MaterialTheme.typography.titleMedium)
        Spacer(Modifier.height(8.dp))

        LazyVerticalGrid(
            columns = GridCells.Fixed(4),
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier.height(120.dp)
        ) {
            items(question.answers) { answer ->
                // Kiểm tra xem đáp án này đã được match chưa (chỉ kiểm tra key vì đây là phần chọn từ trước)
                val isMatched = matchedPairs.containsKey(answer.answers_id)

                Box(
                    modifier = Modifier
                        .height(40.dp)
                        .border(
                            2.dp,
                            Color.White,
                            RoundedCornerShape(8.dp)
                        )
                        .clickable {
                            // Chỉ cho phép chọn nếu chưa được match
                            if (!isMatched) {
                                hasUserInteracted = true // Đánh dấu user đã tương tác
                                // Nếu đang chọn đáp án khác, chuyển sang đáp án này
                                // Nếu đang chọn đáp án này, deselect
                                selectedLeft = if (selectedLeft == answer) null else answer
                            }
                        }
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(
                                when {
                                    selectedLeft == answer -> boxBackgroundColor.copy(alpha = .8f) // Đang được chọn
                                    isMatched -> Color.Gray.copy(alpha = .6f) // Đã được match - màu xám
                                    else -> boxBackgroundColor // Chưa được chọn - màu xanh bình thường
                                },
                                shape = RoundedCornerShape(8.dp)
                            )
                            .padding(horizontal = 8.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = answer.answer_text,
                            color = if (isMatched) Color.DarkGray else Color.White
                        )
                    }
                }
            }
        }

        Spacer(Modifier.height(24.dp))

        Text("Nhấn vào ô bên dưới hình ảnh:", style = MaterialTheme.typography.titleMedium)
        Spacer(Modifier.height(8.dp))

        LazyVerticalGrid(
            columns = GridCells.Fixed(2),
            verticalArrangement = Arrangement.spacedBy(16.dp),
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            modifier = Modifier.weight(1f)
        ) {
            items(question.answers) { answer ->
                val media = answer.media_url ?: ""
                val nameNoExt = media.substringBeforeLast('.')
                val drawableId = remember(nameNoExt) {
                    context.resources.getIdentifier(
                        nameNoExt,
                        "drawable",
                        context.packageName
                    )
                }

                val mediaUrl = answer.media_url
                val matchedLeftEntry = matchedPairs.entries.find { it.value == answer.answers_id }
                val matchedLeft = question.answers.find { it.answers_id == matchedLeftEntry?.key }

                Card(
                    shape = RoundedCornerShape(12.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 6.dp),
                    border = BorderStroke(2.dp, cardBorderColor),
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(0.85f)
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.SpaceBetween,
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(8.dp)
                    ) {
                        // Hiển thị ảnh: nếu có URL thì dùng AsyncImage, không thì placeholder
                        if (!mediaUrl.isNullOrBlank()) {
                            AsyncImage(
                                model = mediaUrl,
                                contentDescription = answer.match_key,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .weight(1f),
                                contentScale = ContentScale.Crop,
                                placeholder = painterResource(R.drawable.placeholder)
                            )
                        } else {
                            Image(
                                painter = painterResource(id = R.drawable.placeholder),
                                contentDescription = answer.match_key,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .weight(1f),
                                contentScale = ContentScale.Crop
                            )
                        }

                        Spacer(Modifier.height(8.dp))

                        Button(
                            onClick = {
                                hasUserInteracted = true // Đánh dấu user đã tương tác

                                if (matchedLeft != null) {
                                    // Unmatch cặp hiện tại
                                    matchedPairs.remove(matchedLeft.answers_id)
                                    // Reset selectedLeft nếu nó đang chọn đáp án vừa được unmatch
                                    if (selectedLeft?.answers_id == matchedLeft.answers_id) {
                                        selectedLeft = null
                                    }
                                } else {
                                    selectedLeft?.let { left ->
                                        // Xóa match cũ nếu có
                                        matchedPairs.entries.find { it.key == left.answers_id }?.let {
                                            matchedPairs.remove(it.key)
                                        }
                                        // Tạo match mới
                                        matchedPairs[left.answers_id] = answer.answers_id
                                        selectedLeft = null
                                    }
                                }

                                val answerString = matchedPairs.entries.joinToString(",") { "${it.key}:${it.value}" }
                                onAnswer(answerString)
                            },
                            shape = RoundedCornerShape(50),
                            border = BorderStroke(
                                2.dp,
                                if (matchedLeft != null) selectedButtonColor else Color.Gray
                            ),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = if (matchedLeft != null) Color.Transparent else Color.LightGray
                            ),
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Text(
                                when {
                                    matchedLeft != null -> matchedLeft.answer_text
                                    selectedLeft != null -> "Chọn từ"
                                    else -> "Chọn từ trước"
                                },
                                color = if (matchedLeft != null) selectedButtonColor else Color.DarkGray,
                                fontWeight = if (matchedLeft != null) FontWeight.Bold else FontWeight.Normal
                            )
                        }
                    }
                }
            }
        }
    }
    // Chỉ gọi onAnswer khi user thực sự thay đổi matchedPairs, không phải khi khởi tạo
    LaunchedEffect(matchedPairs.toMap()) {
        if (hasUserInteracted) {
            val answerString = matchedPairs.entries.joinToString(",") { "${it.key}:${it.value}" }
            onAnswer(answerString)
        }
    }
}
