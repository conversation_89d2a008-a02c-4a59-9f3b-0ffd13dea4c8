package com.example.ckc_englihoo.Screen

import android.util.Log
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.DataClass.*
import com.example.ckc_englihoo.R
import com.example.ckc_englihoo.Utils.formatTimeAgo

/**
 * ClassStreamTeacherScreen - TEACHER ONLY SCREEN
 * Features:
 * - Full CRUD operations on posts (create, view, edit, delete)
 * - View and add comments under posts
 * - Edit/delete only own comments (teacher_id based)
 * - Display actual names of post authors and commenters
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ClassStreamTeacherScreen(
    navController: NavController,
    viewModel: AppViewModel,
    courseId: Int
) {
    val classPosts by viewModel.classPosts.collectAsState()
    val courses by viewModel.courses.collectAsState()
    val currentTeacher by viewModel.currentTeacher.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val teachers by viewModel.teachers.collectAsState()
    val students by viewModel.students.collectAsState()

    val course = courses.find { it.course_id == courseId }

    // Convert course teachers to Teacher objects for compatibility (similar to ClassStreamScreen)
    val courseTeachers = remember(course) {
        if (course?.teachers?.isNotEmpty() == true) {
            Log.d("ClassStreamTeacherScreen", "Converting course.teachers: ${course.teachers.size} teachers")
            course.teachers.map { teacherWithPivot ->
                Teacher(
                    teacher_id = teacherWithPivot.teacher_id,
                    fullname = teacherWithPivot.fullname,
                    username = teacherWithPivot.username,
                    password = teacherWithPivot.password,
                    date_of_birth = teacherWithPivot.date_of_birth,
                    gender = teacherWithPivot.gender,
                    email = teacherWithPivot.email,
                    is_status = teacherWithPivot.is_status,
                    created_at = teacherWithPivot.created_at,
                    updated_at = teacherWithPivot.updated_at
                )
            }.also { convertedTeachers ->
                convertedTeachers.forEach { teacher ->
                    Log.d("ClassStreamTeacherScreen", "Converted teacher: id=${teacher.teacher_id}, name='${teacher.fullname}'")
                }
            }
        } else {
            Log.d("ClassStreamTeacherScreen", "No course.teachers found, using global teachers")
            teachers
        }
    }
    var showCreatePostDialog by remember { mutableStateOf(false) }
    var showEditPostDialog by remember { mutableStateOf(false) }
    var editingPost by remember { mutableStateOf<ClassPost?>(null) }
    var showDeleteDialog by remember { mutableStateOf<ClassPost?>(null) }

    // Load class posts when screen opens
    LaunchedEffect(courseId) {
        viewModel.loadPostsWithCommentsByCourse(courseId)
        viewModel.loadAllCourses()
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF5F5F5))
    ) {
        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(
                    color = Color(0xFF1976D2)
                )
            }
        } else {
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                item {
                    CourseStreamHeader(
                        course = course,
                        teacher = currentTeacher,
                        onCreatePost = { showCreatePostDialog = true }
                    )
                }

                if (classPosts.isEmpty()) {
                    item {
                        EmptyStreamState()
                    }
                } else {
                    items(classPosts) { post ->
                        TeacherClassPostWithCommentsCard(
                            post = post,
                            viewModel = viewModel,
                            currentTeacher = currentTeacher,
                            teachers = courseTeachers,
                            students = students,
                            onEditPost = {
                                editingPost = post
                                showEditPostDialog = true
                            },
                            onDeletePost = {
                                showDeleteDialog = post
                            }
                        )
                    }
                }

                item {
                    Spacer(Modifier.height(80.dp))
                }
            }
        }
    }

    if (showCreatePostDialog) {
        CreatePostDialog(
            onDismiss = { showCreatePostDialog = false },
            onCreatePost = { title, content ->
                // Create new post using API
                viewModel.createClassPost(
                    courseId = courseId,
                    title = title,
                    content = content,
                    teacherId = currentTeacher?.teacher_id ?: 0
                )
                showCreatePostDialog = false
            }
        )
    }

    // Edit Post Dialog
    if (showEditPostDialog && editingPost != null) {
        EditPostDialog(
            post = editingPost!!,
            onDismiss = {
                showEditPostDialog = false
                editingPost = null
            },
            onUpdatePost = { title, content ->
                viewModel.updateClassPost(
                    postId = editingPost!!.post_id,
                    title = title,
                    content = content
                )
                showEditPostDialog = false
                editingPost = null
            }
        )
    }

    // Delete Confirmation Dialog
    showDeleteDialog?.let { post ->
        AlertDialog(
            onDismissRequest = { showDeleteDialog = null },
            title = { Text("Xác nhận xóa") },
            text = { Text("Bạn có chắc chắn muốn xóa bài đăng này không?") },
            confirmButton = {
                Button(
                    onClick = {
                        viewModel.deleteClassPost(post.post_id, courseId)
                        showDeleteDialog = null
                    }
                ) {
                    Text("Xóa")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteDialog = null }
                ) {
                    Text("Hủy")
                }
            }
        )
    }
}

@Composable
private fun CourseStreamHeader(
    course: Course?,
    teacher: Teacher?,
    onCreatePost: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        course?.course_name ?: "Khóa học",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF1976D2)
                    )
                    Text(
                        "Cấp độ: ${course?.level ?: "N/A"} • Năm: ${course?.year ?: "N/A"}",
                        fontSize = 14.sp,
                        color = Color(0xFF666666)
                    )
                    Text(
                        "Học sinh: ${course?.student_count ?: 0}",
                        fontSize = 14.sp,
                        color = Color(0xFF666666)
                    )
                }

                FloatingActionButton(
                    onClick = onCreatePost,
                    containerColor = Color(0xFF1976D2),
                    contentColor = Color.White,
                    modifier = Modifier.size(56.dp)
                ) {
                    Icon(Icons.Default.Add, null)
                }
            }

            Spacer(Modifier.height(16.dp))

            // Teacher info
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(Color(0xFF1976D2)),
                    contentAlignment = Alignment.Center
                ) {
                    Image(
                        painterResource(R.drawable.teacher),
                        null,
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.Crop
                    )
                }

                Spacer(Modifier.width(12.dp))

                Column {
                    Text(
                        teacher?.fullname ?: "Giáo viên",
                        fontWeight = FontWeight.Medium,
                        fontSize = 16.sp
                    )
                    Text(
                        "Giáo viên",
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                }
            }

            Spacer(Modifier.height(16.dp))

            Text(
                course?.description ?: "Không có mô tả",
                fontSize = 14.sp,
                color = Color(0xFF333333)
            )
        }
    }
}

@Composable
private fun StreamPostCard(
    post: ClassPost,
    currentTeacher: Teacher?,
    onEditPost: () -> Unit,
    onDeletePost: () -> Unit
) {
    var showMenu by remember { mutableStateOf(false) }

    // Check if current teacher can edit/delete this post
    val canModify = currentTeacher?.teacher_id == post.teacher_id

    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(40.dp)
                            .clip(CircleShape)
                            .background(Color(0xFF1976D2)),
                        contentAlignment = Alignment.Center
                    ) {
                        Image(
                            painterResource(R.drawable.teacher),
                            null,
                            modifier = Modifier.fillMaxSize(),
                            contentScale = ContentScale.Crop
                        )
                    }

                    Spacer(Modifier.width(12.dp))

                    Column {
                        Text(
                            post.teacher?.fullname ?: "Giáo viên",
                            fontWeight = FontWeight.Medium,
                            fontSize = 16.sp
                        )
                        Text(
                            post.created_at,
                            fontSize = 12.sp,
                            color = Color.Gray
                        )
                    }
                }

                Box {
                    IconButton(onClick = { showMenu = true }) {
                        Icon(Icons.Default.MoreVert, null)
                    }

                    DropdownMenu(
                        expanded = showMenu,
                        onDismissRequest = { showMenu = false }
                    ) {
                        DropdownMenuItem(
                            text = { Text("Chỉnh sửa") },
                            onClick = {
                                showMenu = false
                                onEditPost()
                            },
                            leadingIcon = {
                                Icon(Icons.Default.Edit, null)
                            }
                        )
                        DropdownMenuItem(
                            text = { Text("Xóa") },
                            onClick = {
                                showMenu = false
                                onDeletePost()
                            },
                            leadingIcon = {
                                Icon(Icons.Default.Delete, null)
                            }
                        )
                    }
                }
            }

            Spacer(Modifier.height(12.dp))

            Text(
                post.content,
                fontSize = 14.sp,
                color = Color(0xFF333333),
                lineHeight = 20.sp
            )
        }
    }
}

@Composable
private fun EmptyStreamState() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                Icons.Default.Forum,
                null,
                tint = Color(0xFFBDBDBD),
                modifier = Modifier.size(64.dp)
            )
            Spacer(Modifier.height(16.dp))
            Text(
                "Chưa có bài đăng nào",
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF666666),
                textAlign = TextAlign.Center
            )
            Spacer(Modifier.height(8.dp))
            Text(
                "Hãy tạo bài đăng đầu tiên cho lớp học của bạn",
                fontSize = 14.sp,
                color = Color(0xFF999999),
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun CreatePostDialog(
    onDismiss: () -> Unit,
    onCreatePost: (String, String) -> Unit // title, content
) {
    var postTitle by remember { mutableStateOf("") }
    var postContent by remember { mutableStateOf("") }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                "Tạo bài đăng mới",
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column {
                OutlinedTextField(
                    value = postTitle,
                    onValueChange = { postTitle = it },
                    label = { Text("Tiêu đề bài đăng") },
                    placeholder = { Text("Nhập tiêu đề...") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 1
                )

                Spacer(modifier = Modifier.height(8.dp))

                OutlinedTextField(
                    value = postContent,
                    onValueChange = { postContent = it },
                    label = { Text("Nội dung bài đăng") },
                    placeholder = { Text("Nhập nội dung bài đăng...") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp),
                    maxLines = 5
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    if (postTitle.isNotBlank() && postContent.isNotBlank()) {
                        onCreatePost(postTitle, postContent)
                    }
                },
                enabled = postTitle.isNotBlank() && postContent.isNotBlank()
            ) {
                Text("Đăng")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Hủy")
            }
        }
    )
}

@Composable
private fun EditPostDialog(
    post: ClassPost,
    onDismiss: () -> Unit,
    onUpdatePost: (String, String) -> Unit // title, content
) {
    var postTitle by remember { mutableStateOf(post.title) }
    var postContent by remember { mutableStateOf(post.content) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                "Chỉnh sửa bài đăng",
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column {
                OutlinedTextField(
                    value = postTitle,
                    onValueChange = { postTitle = it },
                    label = { Text("Tiêu đề bài đăng") },
                    placeholder = { Text("Nhập tiêu đề...") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 1
                )

                Spacer(modifier = Modifier.height(8.dp))

                OutlinedTextField(
                    value = postContent,
                    onValueChange = { postContent = it },
                    label = { Text("Nội dung bài đăng") },
                    placeholder = { Text("Nhập nội dung bài đăng...") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp),
                    maxLines = 5
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    if (postTitle.isNotBlank() && postContent.isNotBlank()) {
                        onUpdatePost(postTitle, postContent)
                    }
                },
                enabled = postTitle.isNotBlank() && postContent.isNotBlank()
            ) {
                Text("Cập nhật")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Hủy")
            }
        }
    )
}

@Composable
private fun TeacherClassPostWithCommentsCard(
    post: ClassPost,
    viewModel: AppViewModel,
    currentTeacher: Teacher?,
    teachers: List<Teacher> = emptyList(),
    students: List<Student> = emptyList(),
    onEditPost: () -> Unit,
    onDeletePost: () -> Unit
) {
    var showCommentInput by remember { mutableStateOf(false) }
    var commentText by remember { mutableStateOf("") }
    var editingComment by remember { mutableStateOf<ClassPostComment?>(null) }
    var showDeleteCommentDialog by remember { mutableStateOf<ClassPostComment?>(null) }
    var showAllComments by remember { mutableStateOf(false) }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Post Header with Edit/Delete options
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(40.dp)
                            .clip(CircleShape)
                            .background(MaterialTheme.colorScheme.primaryContainer),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = post.teacher?.fullname?.firstOrNull()?.toString() ?: "T",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }

                    Spacer(modifier = Modifier.width(12.dp))

                    Column {
                        Text(
                            text = post.teacher?.fullname ?: "Giáo viên",
                            style = MaterialTheme.typography.titleSmall,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = formatTimeAgo(post.created_at),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }

                // Edit/Delete menu for post owner
                if (currentTeacher?.teacher_id == post.teacher_id) {
                    var showPostMenu by remember { mutableStateOf(false) }

                    Box {
                        IconButton(onClick = { showPostMenu = true }) {
                            Icon(
                                imageVector = Icons.Default.MoreVert,
                                contentDescription = "Menu"
                            )
                        }

                        DropdownMenu(
                            expanded = showPostMenu,
                            onDismissRequest = { showPostMenu = false }
                        ) {
                            DropdownMenuItem(
                                text = { Text("Chỉnh sửa") },
                                onClick = {
                                    showPostMenu = false
                                    onEditPost()
                                },
                                leadingIcon = {
                                    Icon(Icons.Default.Edit, contentDescription = null)
                                }
                            )
                            DropdownMenuItem(
                                text = { Text("Xóa") },
                                onClick = {
                                    showPostMenu = false
                                    onDeletePost()
                                },
                                leadingIcon = {
                                    Icon(Icons.Default.Delete, contentDescription = null)
                                }
                            )
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // Post Title
            if (post.title.isNotEmpty()) {
                Text(
                    text = post.title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
            }

            // Post Content
            Text(
                text = post.content,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Comments Section
            val comments = post.comments
            if (comments.isNotEmpty()) {
                Text(
                    text = "Bình luận (${comments.size})",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(bottom = 8.dp)
                )

                val commentsToShow = if (showAllComments) comments else comments.take(3)

                commentsToShow.forEach { comment ->
                    TeacherCommentItem(
                        comment = comment,
                        currentTeacher = currentTeacher,
                        onEditComment = {
                            editingComment = comment
                            commentText = comment.content
                            showCommentInput = true
                        },
                        onDeleteComment = { showDeleteCommentDialog = comment }
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }

                if (comments.size > 3) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp),
                        horizontalArrangement = Arrangement.Center
                    ) {
                        TextButton(
                            onClick = { showAllComments = !showAllComments }
                        ) {
                            Icon(
                                imageVector = if (showAllComments) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
                                contentDescription = null,
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(
                                if (showAllComments) {
                                    "Ẩn bớt bình luận"
                                } else {
                                    "Xem thêm ${comments.size - 3} bình luận"
                                },
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                    }
                }
            }

            // Add Comment Section
            if (showCommentInput) {
                Column {
                    OutlinedTextField(
                        value = commentText,
                        onValueChange = { commentText = it },
                        label = {
                            Text(if (editingComment != null) "Chỉnh sửa bình luận" else "Thêm bình luận")
                        },
                        modifier = Modifier.fillMaxWidth(),
                        maxLines = 3
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Row(
                        horizontalArrangement = Arrangement.End,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        TextButton(
                            onClick = {
                                showCommentInput = false
                                commentText = ""
                                editingComment = null
                            }
                        ) {
                            Text("Hủy")
                        }

                        Spacer(modifier = Modifier.width(8.dp))

                        Button(
                            onClick = {
                                if (commentText.isNotBlank()) {
                                    if (editingComment != null) {
                                        // Update comment - only if teacher owns the comment
                                        if (currentTeacher != null && editingComment!!.author_id == currentTeacher.teacher_id) {
                                            Log.d("ClassStreamTeacherScreen", "Updating comment ${editingComment!!.comment_id} with content: '$commentText'")
                                            viewModel.updateClassPostComment(
                                                commentId = editingComment!!.comment_id,
                                                content = commentText
                                            )
                                        } else {
                                            Log.e("ClassStreamTeacherScreen", "Permission denied: Cannot update comment ${editingComment!!.comment_id}")
                                        }
                                    } else {
                                        // Create new comment (only teachers in TeacherScreen)
                                        viewModel.createClassPostComment(
                                            postId = post.post_id,
                                            content = commentText,
                                            studentId = null, // No student comments in teacher screen
                                            teacherId = currentTeacher?.teacher_id
                                        )
                                    }
                                    showCommentInput = false
                                    commentText = ""
                                    editingComment = null
                                }
                            },
                            enabled = commentText.isNotBlank()
                        ) {
                            Icon(
                                imageVector = Icons.Default.Send,
                                contentDescription = null,
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(if (editingComment != null) "Cập nhật" else "Gửi")
                        }
                    }
                }
            } else {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { showCommentInput = true }
                        .padding(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Comment,
                        contentDescription = "Icon bình luận",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "Thêm bình luận...",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }

    // Delete Comment Confirmation Dialog
    showDeleteCommentDialog?.let { comment ->
        AlertDialog(
            onDismissRequest = { showDeleteCommentDialog = null },
            title = { Text("Xác nhận xóa") },
            text = { Text("Bạn có chắc chắn muốn xóa bình luận này không?") },
            confirmButton = {
                Button(
                    onClick = {
                        // Double-check permission before deleting
                        if (currentTeacher != null && comment.author_id == currentTeacher.teacher_id) {
                            Log.d("ClassStreamTeacherScreen", "Deleting comment ${comment.comment_id}")
                            viewModel.deleteClassPostComment(comment.comment_id)
                        } else {
                            Log.e("ClassStreamTeacherScreen", "Permission denied: Cannot delete comment ${comment.comment_id}")
                        }
                        showDeleteCommentDialog = null
                    }
                ) {
                    Text("Xóa")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteCommentDialog = null }
                ) {
                    Text("Hủy")
                }
            }
        )
    }
}

@Composable
private fun TeacherCommentItem(
    comment: ClassPostComment,
    currentTeacher: Teacher?,
    teachers: List<Teacher> = emptyList(),
    students: List<Student> = emptyList(),
    onEditComment: () -> Unit = {},
    onDeleteComment: () -> Unit = {}
) {
    var showMenu by remember { mutableStateOf(false) }

    // Check if current teacher can edit/delete this comment (only teacher comments in TeacherScreen)
    val canModify = currentTeacher != null && comment.author_id == currentTeacher.teacher_id

    // Debug logging for permission check
    Log.d("TeacherCommentItem", "Permission check for comment ${comment.comment_id}:")
    Log.d("TeacherCommentItem", "  currentTeacher: ${currentTeacher?.teacher_id}")
    Log.d("TeacherCommentItem", "  comment.teacher_id: ${comment.teacher_id}")
    Log.d("TeacherCommentItem", "  comment.student_id: ${comment.student_id}")
    Log.d("TeacherCommentItem", "  canModify: $canModify")

    // TEMPORARY: Show menu for all teacher comments for testing
    val showMenuForTesting = comment.author_id != null && currentTeacher != null
    Log.d("TeacherCommentItem", "  showMenuForTesting: $showMenuForTesting")

    val isOwnComment = canModify

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(start = 8.dp)
            .then(
                if (isOwnComment) {
                    Modifier
                        .background(
                            MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f),
                            RoundedCornerShape(8.dp)
                        )
                        .padding(8.dp)
                } else {
                    Modifier
                }
            ),
        verticalAlignment = Alignment.Top
    ) {
        // Avatar comment author with different colors for student/teacher
        Box(
            modifier = Modifier
                .size(32.dp)
                .clip(CircleShape)
                .background(
                    when {
                        comment.student_id != null -> MaterialTheme.colorScheme.primaryContainer
                        comment.teacher_id != null -> MaterialTheme.colorScheme.tertiaryContainer
                        else -> MaterialTheme.colorScheme.secondaryContainer
                    }
                ),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = when {
                    !comment.author_name.isNullOrBlank() -> comment.author_name.firstOrNull()?.toString()?.uppercase() ?: "U"
                    comment.student_id != null -> "S"
                    comment.teacher_id != null -> "T"
                    else -> "U"
                },
                style = MaterialTheme.typography.bodySmall,
                fontWeight = FontWeight.Bold,
                color = when {
                    comment.student_id != null -> MaterialTheme.colorScheme.onPrimaryContainer
                    comment.teacher_id != null -> MaterialTheme.colorScheme.onTertiaryContainer
                    else -> MaterialTheme.colorScheme.onSecondaryContainer
                }
            )
        }

        Spacer(modifier = Modifier.width(8.dp))

        Column(modifier = Modifier.weight(1f)) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier.fillMaxWidth()
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        // Enhanced comment author name resolution
                        val authorName = remember(comment, teachers, students) {
                            Log.d("TeacherCommentItem", "🔍 RESOLVING comment ${comment.comment_id}:")
                            Log.d("TeacherCommentItem", "  📝 author_name='${comment.author_name}'")
                            Log.d("TeacherCommentItem", "  👨‍🏫 teacher_id=${comment.teacher_id}, 👨‍🎓 student_id=${comment.student_id}")
                            Log.d("TeacherCommentItem", "  🏷️ author_type='${comment.author_type}', 🆔 author_id=${comment.author_id}")
                            Log.d("TeacherCommentItem", "  📦 author object: ${comment.author}")
                            Log.d("TeacherCommentItem", "  📊 Available: teachers=${teachers.size}, students=${students.size}")

                            val result = when {
                                // 1. Try author object first (most reliable)
                                comment.author != null -> {
                                    try {
                                        when (comment.author) {
                                            is Map<*, *> -> {
                                                val authorMap = comment.author as Map<String, Any>
                                                val fullname = authorMap["fullname"] as? String
                                                if (!fullname.isNullOrBlank()) {
                                                    val cleanedName = fullname.replace("Không xác định", "").trim()
                                                    if (cleanedName.isNotBlank()) {
                                                        Log.d("TeacherCommentItem", "✅ Using author object fullname: '$cleanedName'")
                                                        cleanedName
                                                    } else {
                                                        Log.d("TeacherCommentItem", "❌ Author object fullname is just 'Không xác định'")
                                                        "Người dùng"
                                                    }
                                                } else {
                                                    Log.d("TeacherCommentItem", "❌ Author object has no fullname")
                                                    "Người dùng"
                                                }
                                            }
                                            else -> {
                                                Log.d("TeacherCommentItem", "❌ Author object is not a Map: ${comment.author::class.simpleName}")
                                                "Người dùng"
                                            }
                                        }
                                    } catch (e: Exception) {
                                        Log.d("TeacherCommentItem", "❌ Error parsing author object: ${e.message}")
                                        "Người dùng"
                                    }
                                }

                                // 2. Try author_name if available and clean
                                !comment.author_name.isNullOrBlank() -> {
                                    val cleanedName = comment.author_name.replace("Không xác định", "").trim()
                                    if (cleanedName.isNotBlank()) {
                                        Log.d("TeacherCommentItem", "✅ Using cleaned author_name: '$cleanedName'")
                                        cleanedName
                                    } else {
                                        Log.d("TeacherCommentItem", "❌ Author name is just 'Không xác định', trying other methods")
                                        "Người dùng"
                                    }
                                }

                                // 3. Try to find by teacher_id (this usually works for teacher screen)
                                comment.teacher_id != null -> {
                                    val teacher = teachers.find { it.teacher_id == comment.teacher_id }
                                    val name = teacher?.fullname ?: "Giáo viên"
                                    Log.d("TeacherCommentItem", "✅ Teacher by teacher_id=${comment.teacher_id} -> '$name'")
                                    name
                                }

                                // 4. Try to find by student_id
                                comment.student_id != null -> {
                                    val student = students.find { it.student_id == comment.student_id }
                                    val name = student?.fullname ?: "Học sinh"
                                    Log.d("TeacherCommentItem", "✅ Student by student_id=${comment.student_id} -> '$name'")
                                    name
                                }

                                // 5. Try author_type + author_id
                                comment.author_id > 0 && comment.author_type.isNotBlank() -> {
                                    when (comment.author_type.lowercase()) {
                                        "teacher" -> {
                                            val teacher = teachers.find { it.teacher_id == comment.author_id }
                                            val name = teacher?.fullname ?: "Giáo viên"
                                            Log.d("TeacherCommentItem", "✅ Teacher by author_id=${comment.author_id} -> '$name'")
                                            name
                                        }
                                        "student" -> {
                                            val student = students.find { it.student_id == comment.author_id }
                                            val name = student?.fullname ?: "Học sinh"
                                            Log.d("TeacherCommentItem", "✅ Student by author_id=${comment.author_id} -> '$name'")
                                            name
                                        }
                                        else -> {
                                            Log.d("TeacherCommentItem", "❌ Unknown author_type: '${comment.author_type}'")
                                            "Người dùng"
                                        }
                                    }
                                }

                                else -> {
                                    Log.d("TeacherCommentItem", "❌ No valid author info found")
                                    "Người dùng"
                                }
                            }

                            Log.d("TeacherCommentItem", "🎯 FINAL RESULT: '$result'")
                            result
                        }

                        Text(
                            text = authorName,
                            style = MaterialTheme.typography.bodySmall,
                            fontWeight = FontWeight.Medium,
                            color = MaterialTheme.colorScheme.onSurface
                        )

                        if (isOwnComment) {
                            Text(
                                text = "Bạn",
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.primary,
                                modifier = Modifier
                                    .background(
                                        MaterialTheme.colorScheme.primaryContainer,
                                        RoundedCornerShape(4.dp)
                                    )
                                    .padding(horizontal = 4.dp, vertical = 1.dp)
                            )
                        }
                    }
                    Text(
                        text = formatTimeAgo(comment.created_at),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                // Menu button for edit/delete - ALWAYS SHOW FOR TESTING
                Log.d("TeacherCommentItem", "Showing menu for comment ${comment.comment_id}: canModify=$canModify, showMenuForTesting=$showMenuForTesting")
                if (canModify || showMenuForTesting) {
                    Box {
                        IconButton(
                            onClick = {
                                Log.d("TeacherCommentItem", "Menu button clicked for comment ${comment.comment_id}")
                                showMenu = true
                            },
                            modifier = Modifier
                                .size(32.dp)
                                .background(
                                    color = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                                    shape = CircleShape
                                )
                        ) {
                            Icon(
                                imageVector = Icons.Default.MoreVert,
                                contentDescription = "Menu",
                                modifier = Modifier.size(20.dp),
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }

                        DropdownMenu(
                            expanded = showMenu,
                            onDismissRequest = { showMenu = false }
                        ) {
                            DropdownMenuItem(
                                text = { Text("Chỉnh sửa") },
                                onClick = {
                                    showMenu = false
                                    onEditComment()
                                },
                                leadingIcon = {
                                    Icon(
                                        imageVector = Icons.Default.Edit,
                                        contentDescription = null,
                                        modifier = Modifier.size(16.dp)
                                    )
                                }
                            )
                            DropdownMenuItem(
                                text = { Text("Xóa") },
                                onClick = {
                                    showMenu = false
                                    onDeleteComment()
                                },
                                leadingIcon = {
                                    Icon(
                                        imageVector = Icons.Default.Delete,
                                        contentDescription = null,
                                        modifier = Modifier.size(16.dp)
                                    )
                                }
                            )
                        }
                    }
                }
            }

            Text(
                text = comment.content,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.padding(top = 2.dp)
            )
        }
    }
}

