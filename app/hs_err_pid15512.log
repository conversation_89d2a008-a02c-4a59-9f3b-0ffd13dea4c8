#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 134217728 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3528), pid=15512, tid=10904
#
# JRE version:  (20.0.2+9) (build )
# Java VM: Java HotSpot(TM) 64-Bit Server VM (20.0.2*****, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -Djava.awt.headless=true -Djava.library.path=C:\Program Files\Java\jdk-20\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\flutter\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files\Microsoft MPI\Bin\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Azure Data Studio\bin;C:\Program Files (x86)\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Java\jdk-20\bin;C:\kotlinc\bin;D:\Downloads\apache-maven-3.9.9\bin;C:\Program Files\dotnet\;C:\Program Files\Git\cmd;D:\xampp\php;D:\Compose;C:\flutter\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2023.2\bin;;C:\Program Files\Azure Data Studio\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;D:\software\IntelliJ IDEA Community Edition 2024.2.1\bin;;C:\Program Files\Git\bin\git;D:\software\Microsoft VS Code\visualcode\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;.;C:\Users\<USER>\Desktop\DOAN-THUCTAP\CKC-Englishoo\app\src\testRelease\jniLibs;C:\Users\<USER>\Desktop\DOAN-THUCTAP\CKC-Englishoo\app\src\test\jniLibs -Dorg.gradle.internal.worker.tmpdir=C:\Users\<USER>\Desktop\DOAN-THUCTAP\CKC-Englishoo\app\build\tmp\testReleaseUnitTest\work --add-opens=java.base/java.io=ALL-UNNAMED -Xmx512m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -ea worker.org.gradle.process.internal.worker.GradleWorkerMain 'Gradle Test Executor 1'

Host: Intel(R) Core(TM) i5-3210M CPU @ 2.50GHz, 4 cores, 7G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Sun Jul  6 04:25:03 2025 SE Asia Standard Time elapsed time: 0.078878 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x0000020007c66dd0):  JavaThread "Unknown thread" [_thread_in_vm, id=10904, stack(0x00000033ff600000,0x00000033ff700000)]

Stack: [0x00000033ff600000,0x00000033ff700000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6bc25a]
V  [jvm.dll+0x84971a]
V  [jvm.dll+0x84b315]
V  [jvm.dll+0x84ba13]
V  [jvm.dll+0x280e4f]
V  [jvm.dll+0x6b8fe9]
V  [jvm.dll+0x6adbca]
V  [jvm.dll+0x35b165]
V  [jvm.dll+0x363386]
V  [jvm.dll+0x3b400e]
V  [jvm.dll+0x3b4295]
V  [jvm.dll+0x32b86a]
V  [jvm.dll+0x32c68b]
V  [jvm.dll+0x8123df]
V  [jvm.dll+0x3c1261]
V  [jvm.dll+0x7fe103]
V  [jvm.dll+0x44ccaf]
V  [jvm.dll+0x44e621]
C  [jli.dll+0x5287]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ff93badb118, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x0000020007c98c90 WorkerThread "GC Thread#0" [stack: 0x00000033ff800000,0x00000033ff900000] [id=9596]
  0x0000020007c9dab0 ConcurrentGCThread "G1 Main Marker" [stack: 0x00000033ff900000,0x00000033ffa00000] [id=13976]
  0x0000020007c9e3f0 WorkerThread "G1 Conc#0" [stack: 0x00000033ffa00000,0x00000033ffb00000] [id=10220]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff93b2beea7]
VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x0000020007c60e20] Heap_lock - owner thread: 0x0000020007c66dd0

Heap address: 0x00000000e0000000, size: 512 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048

Heap:
 garbage-first heap   total 0K, used 0K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 0 young (0K), 0 survivors (0K)

[error occurred during error reporting (printing heap information), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff93b6947b9]
GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.020 Loaded shared library C:\Program Files\Java\jdk-20\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff608710000 - 0x00007ff608720000 	C:\Program Files\Java\jdk-20\bin\java.exe
0x00007ff992550000 - 0x00007ff992748000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff990f30000 - 0x00007ff990ff2000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff98fe30000 - 0x00007ff990126000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff9902a0000 - 0x00007ff9903a0000 	C:\Windows\System32\ucrtbase.dll
0x00007ff96e600000 - 0x00007ff96e619000 	C:\Program Files\Java\jdk-20\bin\jli.dll
0x00007ff991f70000 - 0x00007ff992021000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff990c30000 - 0x00007ff990cce000 	C:\Windows\System32\msvcrt.dll
0x00007ff990b90000 - 0x00007ff990c2f000 	C:\Windows\System32\sechost.dll
0x00007ff991140000 - 0x00007ff991263000 	C:\Windows\System32\RPCRT4.dll
0x00007ff98fdd0000 - 0x00007ff98fdf7000 	C:\Windows\System32\bcrypt.dll
0x00007ff992250000 - 0x00007ff9923ed000 	C:\Windows\System32\USER32.dll
0x00007ff98fe00000 - 0x00007ff98fe22000 	C:\Windows\System32\win32u.dll
0x00007ff992220000 - 0x00007ff99224b000 	C:\Windows\System32\GDI32.dll
0x00007ff990450000 - 0x00007ff990569000 	C:\Windows\System32\gdi32full.dll
0x00007ff990200000 - 0x00007ff99029d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff96e5e0000 - 0x00007ff96e5fb000 	C:\Program Files\Java\jdk-20\bin\VCRUNTIME140.dll
0x00007ff983f50000 - 0x00007ff9841ea000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff986400000 - 0x00007ff98640a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff9920c0000 - 0x00007ff9920ef000 	C:\Windows\System32\IMM32.DLL
0x00007ff97da50000 - 0x00007ff97da5c000 	C:\Program Files\Java\jdk-20\bin\vcruntime140_1.dll
0x00007ff969760000 - 0x00007ff9697ee000 	C:\Program Files\Java\jdk-20\bin\msvcp140.dll
0x00007ff93af80000 - 0x00007ff93bc18000 	C:\Program Files\Java\jdk-20\bin\server\jvm.dll
0x00007ff97d690000 - 0x00007ff97d699000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ff9915e0000 - 0x00007ff99164b000 	C:\Windows\System32\WS2_32.dll
0x00007ff986390000 - 0x00007ff9863b7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff98da60000 - 0x00007ff98da72000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff97d4e0000 - 0x00007ff97d4ea000 	C:\Program Files\Java\jdk-20\bin\jimage.dll
0x00007ff9811a0000 - 0x00007ff9813a1000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff977530000 - 0x00007ff977564000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff98fbe0000 - 0x00007ff98fc62000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff9652f0000 - 0x00007ff965316000 	C:\Program Files\Java\jdk-20\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-20\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;C:\Program Files\Java\jdk-20\bin\server

VM Arguments:
jvm_args: -Djava.awt.headless=true -Djava.library.path=C:\Program Files\Java\jdk-20\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\flutter\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files\Microsoft MPI\Bin\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Azure Data Studio\bin;C:\Program Files (x86)\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Java\jdk-20\bin;C:\kotlinc\bin;D:\Downloads\apache-maven-3.9.9\bin;C:\Program Files\dotnet\;C:\Program Files\Git\cmd;D:\xampp\php;D:\Compose;C:\flutter\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2023.2\bin;;C:\Program Files\Azure Data Studio\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;D:\software\IntelliJ IDEA Community Edition 2024.2.1\bin;;C:\Program Files\Git\bin\git;D:\software\Microsoft VS Code\visualcode\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;.;C:\Users\<USER>\Desktop\DOAN-THUCTAP\CKC-Englishoo\app\src\testRelease\jniLibs;C:\Users\<USER>\Desktop\DOAN-THUCTAP\CKC-Englishoo\app\src\test\jniLibs -Dorg.gradle.internal.worker.tmpdir=C:\Users\<USER>\Desktop\DOAN-THUCTAP\CKC-Englishoo\app\build\tmp\testReleaseUnitTest\work --add-opens=java.base/java.io=ALL-UNNAMED -Xmx512m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -ea 
java_command: worker.org.gradle.process.internal.worker.GradleWorkerMain 'Gradle Test Executor 1'
java_class_path (initial): C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\workerMain\\gradle-worker.jar;C:\\Users\\<USER>\\Desktop\\DOAN-THUCTAP\\CKC-Englishoo\\app\\build\\tmp\\kotlin-classes\\releaseUnitTest;C:\\Users\\<USER>\\Desktop\\DOAN-THUCTAP\\CKC-Englishoo\\app\\build\\intermediates\\java_res\\releaseUnitTest\\processReleaseUnitTestJavaRes\\out;C:\\Users\\<USER>\\Desktop\\DOAN-THUCTAP\\CKC-Englishoo\\app\\build\\intermediates\\runtime_app_classes_jar\\release\\bundleReleaseClassesToRuntimeJar\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\junit\\junit\\4.13.2\\8ac9e16d933b6fb43bc7f576336b8f4d7eb5ba12\\junit-4.13.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\71a960cb4f95f8a05ee08784eae465ad\\transformed\\coil-compose-2.4.0-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\de9e9f10e9daeb0210c66d2f9776ee3c\\transformed\\coil-compose-base-2.4.0-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c88a111f686b5e98bb7b14e40c0b4fab\\transformed\\coil-2.4.0-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\abbe3f680ac6d86a99b2a04a8229ebd8\\transformed\\accompanist-pager-indicators-0.28.0-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\707a0997772690eca4df4cda8ffea668\\transformed\\accompanist-pager-0.28.0-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\db562135ae457d2594ce47ad4b5b24e9\\transformed\\snapper-0.2.2-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e4f93184cc1f22ced173db2bbea4a79d\\transformed\\navigation-common-2.7.4-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5db80ecbdc69ab8108a1f4b609da6f42\\transformed\\navigation-runtime-2.7.4-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bbe36d909d897f3cbce32df76eea8d18\\transformed\\navigation-common-ktx-2.7.4-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3872c413d5c3ec221bec6572d8db2ca4\\transformed\\navigation-runtime-ktx-2.7.4-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\da3dd68674f08a8c395ae7941dec3b0e\\transformed\\navigation-compose-2.7.4-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\19a379ec14e2fbcc98c41d4e77ddd63b\\transformed\\activity-1.8.2-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9f4c289e76a1117bf798eb9692e5f97f\\transformed\\material3-release-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bf44a14564dc99c3c3aab535ffb6ae98\\transformed\\activity-compose-1.8.2-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3277b09b1eb9becbc5742e16b326c633\\transformed\\accompanist-drawablepainter-0.30.1-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8ca52182b802bbda9e2fa6002e0c0665\\transformed\\material-ripple-release-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d0a504bc5e1609484fbb190488140e03\\transformed\\material-icons-core-release-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1de04c8145c43d56511d7a8ef910d876\\transformed\\material-icons-extended-release-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b71723f169b748f5a87aee07d364912a\\transformed\\material-release-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1efed8346901007d9631f99c0d155303\\transformed\\foundation-layout-release-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\345bef6fc7d5b34b6411d57fb51464c7\\transformed\\foundation-release-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f59161b08bc9481cf061fd4f5644b246\\transformed\\animation-core-release-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\30810f5565332da5e616062e809214b4\\transformed\\animation-release-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0fc18f8dd0fcf77ed431154ab587de4b\\transformed\\ui-unit-release-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f1f5469d5ea898efabf51c5e846a10dd\\transformed\\ui-text-release-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d00b4b192ed4d5255b949d94c7d564d0\\transformed\\ui-util-release-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ab0dd2b781dff5ebd20db334abd9890f\\transformed\\ui-geometry-release-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d91aeb90aaba82b6cb90a2474959cec5\\transformed\\ui-tooling-preview-release-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b2d517d4a2005bc71ada6ee0fc4dac5e\\transformed\\ui-graphics-release-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fac5bcb39b0cee0ce2283ba7ff759c6f\\transformed\\ui-release-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f76ee505e25576e1b43bd37ec213dbdc\\transformed\\activity-ktx-1.8.2-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2207c4a6a011c7b7e79510fd8ce5519c\\transformed\\coil-base-2.4.0-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\60b68c2cd6b8a42bb1ae31e96cb930b4\\transformed\\emoji2-1.3.0-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\40f4f6f86f41b3ab4115f373571bcb73\\transformed\\lifecycle-process-2.7.0-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8f78147c5c7ff522adeabdbfb7b86d72\\transformed\\lifecycle-livedata-core-2.7.0-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b737adb00a76d56f7c95de27ff75d43d\\transformed\\savedstate-ktx-1.2.1-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\85aa9549a2b21d724a4a4f4e000b69de\\transformed\\savedstate-1.2.1-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\800e58205f1f2c29ce5bd462b2ef6702\\transformed\\lifecycle-viewmodel-ktx-2.7.0-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7415b3abed205b46e47c7784a14616c3\\transformed\\lifecycle-viewmodel-2.7.0-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7c80051748b9409628d9993d0c690742\\transformed\\lifecycle-viewmodel-savedstate-2.7.0-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\99fb908a8ff538fba06da2d6e30bfdc2\\transformed\\customview-poolingcontainer-1.0.0-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7f8fe0456a400a20b12f59d5d8a6c7dc\\transformed\\core-ktx-1.12.0-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3a48770ce9e701cbe3a164afb06c11c9\\transformed\\appcompat-resources-1.6.1-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ada0fe3375523115f8fb6bb49e025349\\transformed\\autofill-1.0.0-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6c9d44e2c39696fc637ea42303d36d61\\transformed\\vectordrawable-animated-1.1.0-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d2e7b6b46953d76a539a272c3297b93b\\transformed\\vectordrawable-1.1.0-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\47223a6a489c62dcfbe9272664c40be5\\transformed\\core-1.12.0-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7dc42ef166b7c700a409d8fc0ae6d7d2\\transformed\\lifecycle-runtime-2.7.0-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cec796f0df309525628a5aa6f7886fae\\transformed\\lifecycle-viewmodel-compose-2.7.0-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4eb9c724e5bfeb2a3c80a1aee24e3003\\transformed\\lifecycle-runtime-ktx-2.7.0-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bed8002834d7176593ce1465d283bea8\\transformed\\runtime-saveable-release-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5c8405cb3f9a529a8e032afae0c40ed2\\transformed\\runtime-release-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-coroutines-core-jvm\\1.7.1\\63a0779cf668e2a47d13fda7c3b0c4f8dc7762f4\\kotlinx-coroutines-core-jvm-1.7.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-coroutines-android\\1.7.1\\c2d86b569f10b7fc7e28d3f50c0eed97897d77a7\\kotlinx-coroutines-android-1.7.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.squareup.retrofit2\\converter-gson\\2.9.0\\fc93484fc67ab52b1e0ccbdaa3922d8a6678e097\\converter-gson-2.9.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.squareup.retrofit2\\retrofit\\2.9.0\\d8fdfbd5da952141a665a403348b74538efc05ff\\retrofit-2.9.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.squareup.okhttp3\\okhttp\\4.11.0\\436932d695b2c43f2c86b8111c596179cd133d56\\okhttp-4.11.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.squareup.okio\\okio-jvm\\3.3.0\\2d175add2d06a67bda111ae5455e49b42d0bb287\\okio-jvm-3.3.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib-jdk8\\1.9.0\\e000bd084353d84c9e888f6fb341dc1f5b79d948\\kotlin-stdlib-jdk8-1.9.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.code.gson\\gson\\2.10.1\\b3add478d4382b78ea20b1671390a858002feb6c\\gson-2.10.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.caverock\\androidsvg\\1.4\\4accacfa9d497b964c5ca6d7a073af60fb3ebd63\\androidsvg-1.4.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.hamcrest\\hamcrest-core\\1.3\\42a25dc3219429f0e5d060061f71acb49bf010a0\\hamcrest-core-1.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib-jdk7\\1.9.0\\f320478990d05e0cfaadd74f9619fd6027adbf37\\kotlin-stdlib-jdk7-1.9.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\009409506e25263eafec6ba227a7fc14\\transformed\\napier-release-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ca9491e215a74493cfaa90082ced8503\\transformed\\annotation-experimental-1.4.0-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5cdf315e4453e25881060e7d186dc9fd\\transformed\\versionedparcelable-1.1.1-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection-ktx\\1.4.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\collection-ktx-1.4.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection-jvm\\1.4.0\\e209fb7bd1183032f55a0408121c6251a81acb49\\collection-jvm-1.4.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\790647c5e3d3c07589b403be7b1c816d\\transformed\\interpolator-1.0.0-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\411502cc15c55453281408bdaf29c5fb\\transformed\\core-runtime-2.2.0-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\797febec1b61118802cde03225193ed1\\transformed\\profileinstaller-1.3.1-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b6c8671e67bc66d80108ed659623230a\\transformed\\exifinterface-1.3.6-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e7d153c4ea5ee4d27d3da6ccc9c77298\\transformed\\startup-runtime-1.1.1-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b39f3254ae5b7cdcc983bd049e894f62\\transformed\\tracing-1.0.0-runtime.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.concurrent\\concurrent-futures\\1.1.0\\50b7fb98350d5f42a4e49704b03278542293ba48\\concurrent-futures-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.annotation\\annotation-jvm\\1.7.0\\920472d40adcdef5e18708976b3e314f9a636fcd\\annotation-jvm-1.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib\\1.9.0\\8ee15ef0c67dc83d874f412d84378d7f0eb50b63\\kotlin-stdlib-1.9.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib-common\\1.9.0\\cd65c21cfd1eec4d44ef09f9f52b6d9f8a720636\\kotlin-stdlib-common-1.9.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains\\annotations\\23.0.0\\8cc20c07506ec18e0834947b84a864bfc094484e\\annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.guava\\listenablefuture\\1.0\\c949a840a6acbc5268d088e47b04177bf90b3cad\\listenablefuture-1.0.jar;C:\\Users\\<USER>\\Desktop\\DOAN-THUCTAP\\CKC-Englishoo\\app\\build\\intermediates\\java_res\\release\\processReleaseJavaRes\\out;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cdd201bad63f20e58b234a5197539423\\transformed\\coil-compose-2.4.0\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aa65dda9f95c4af53aea2f4feadf74f8\\transformed\\coil-compose-base-2.4.0\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\07e0d1f4b55211182a2e7674da4149b0\\transformed\\coil-2.4.0\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\abc4b5eebf4ce5a0dfd053b3cbb53509\\transformed\\accompanist-pager-indicators-0.28.0\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d33bb5b9ea9c8fa62879a2c9c8a06691\\transformed\\accompanist-pager-0.28.0\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e72917da053437e1222fb818d8a3ea5e\\transformed\\snapper-0.2.2\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\47ca9ef4c06beb014db72c0b00b69cb8\\transformed\\navigation-common-2.7.4\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a59b056e1a931ade7d9341b682e4840b\\transformed\\navigation-runtime-2.7.4\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2183583d80be1768564b9a77e327af76\\transformed\\navigation-common-ktx-2.7.4\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\22691e3e4233f44a6fa80f07eef8717f\\transformed\\navigation-runtime-ktx-2.7.4\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5b2aa601de3baeeb8554fa18b73fe95d\\transformed\\navigation-compose-2.7.4\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\325c96cc73ba80a6a4aa3c287ff40aa1\\transformed\\activity-1.8.2\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\51837230bb537a3899dee7f74f11f80a\\transformed\\material3-release\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\74331bef2baed61f02ebc42b5d500e78\\transformed\\activity-compose-1.8.2\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a276dbf6962919b5257c8b6288ecdc47\\transformed\\accompanist-drawablepainter-0.30.1\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\da88a100873e3b00d114837937212a62\\transformed\\material-ripple-release\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\000e3257c0e9cc6b3443344708f32904\\transformed\\material-icons-core-release\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d9ca0838e43cb37f155b60753e39d5c8\\transformed\\material-icons-extended-release\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\56598fad5ef7e1e0912071138e48c292\\transformed\\material-release\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ee10eb396c2d2fa7fbf75c2442940844\\transformed\\foundation-layout-release\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bfebdbcfa881cbee67b0358b1551f3b6\\transformed\\foundation-release\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\af46767ef2a738c0406afb3e31d07ff5\\transformed\\animation-core-release\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\779d5f938c04d97224068127892e02a5\\transformed\\animation-release\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ed3c0e01c6dfe98ab5df63902ee5aa2e\\transformed\\ui-unit-release\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e23b3c65a40a0bcde8cfc8ad2812a090\\transformed\\ui-text-release\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6335f17eb96a16bc362ec65e0b721e27\\transformed\\ui-util-release\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fe5765b3ebee76d1e60697ecc83cbd32\\transformed\\ui-geometry-release\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\47f8188fadfa23b8295fe755747f1943\\transformed\\ui-tooling-preview-release\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\060ecb12ad2829e56ace5267155c74ab\\transformed\\ui-graphics-release\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7dd3cc13c96e31c80840ea4f04ce00af\\transformed\\ui-release\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5f28be6841bc1c127e198d0a588eec2d\\transformed\\activity-ktx-1.8.2\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\63f59f1dc77d3496a19ae90af4e646f9\\transformed\\coil-base-2.4.0\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bf9d92c36ae459e9d58c8c428bbae812\\transformed\\emoji2-1.3.0\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bf9d92c36ae459e9d58c8c428bbae812\\transformed\\emoji2-1.3.0\\jars\\libs\\repackaged.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\abb1c7a91442486d6006234faf7bcfa0\\transformed\\lifecycle-process-2.7.0\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7b97786f6fd8680a3c470dfa9940ecc9\\transformed\\lifecycle-livedata-core-2.7.0\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ee5adb6ce1a9df6ef7bb9973e589c28e\\transformed\\savedstate-ktx-1.2.1\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0d44c039645767fe3faa4de0f1bf7f96\\transformed\\savedstate-1.2.1\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\df5303be52e6668e8afe14939b973759\\transformed\\lifecycle-viewmodel-ktx-2.7.0\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ac4ac71f42198eb7e60b9e47940860fc\\transformed\\lifecycle-viewmodel-2.7.0\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f205faa322b24118620ccb9531fd684e\\transformed\\lifecycle-viewmodel-savedstate-2.7.0\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\83d0831580398fdd6389890e20a28bf5\\transformed\\customview-poolingcontainer-1.0.0\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fdc1c96d4bc38982b86b700e812c1c02\\transformed\\core-ktx-1.12.0\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\de8ac709dd06ae116348b2ea03b09e15\\transformed\\appcompat-resources-1.6.1\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\977ba1f7fc06e8cfbeb2e570cee0ff5e\\transformed\\autofill-1.0.0\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50657015968ae20a68bdc2e7bcb7ed27\\transformed\\vectordrawable-animated-1.1.0\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1b9263c8bbd6707a35267b8d043f7d7a\\transformed\\vectordrawable-1.1.0\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\337dc4b19b454bc030aba700c101fa66\\transformed\\core-1.12.0\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9a7886884003bb142578fb6f866a8c2f\\transformed\\lifecycle-runtime-2.7.0\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\81fa468b0fad40060235efc94ee71fef\\transformed\\lifecycle-viewmodel-compose-2.7.0\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e4efc369e044e43b5374f150233db188\\transformed\\lifecycle-runtime-ktx-2.7.0\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a1d20d800a4e86b64ff8dbf824080147\\transformed\\runtime-saveable-release\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a455d66e57c0479b2e8aab6624af083b\\transformed\\runtime-release\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\23105791f24a1d2b51ef2bc2099b51b6\\transformed\\napier-release\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3d223474c9809cca81739081a99aab38\\transformed\\annotation-experimental-1.4.0\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c90a3ebd585105be5fa775e44c984d8d\\transformed\\versionedparcelable-1.1.1\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\20a22a90832fff0c22a032b92e6ed173\\transformed\\interpolator-1.0.0\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fcf2e2dec894235be124a4ecaa1fb932\\transformed\\core-runtime-2.2.0\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\95e3935258bedbfb6d4812851f828fba\\transformed\\profileinstaller-1.3.1\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b96a36a0635ae7171967a782492ad7c2\\transformed\\exifinterface-1.3.6\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5c6b58319b9f2b848972717bd6331ae3\\transformed\\startup-runtime-1.1.1\\jars\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7e58cb04900329798da4b545a43d9dff\\transformed\\tracing-1.0.0\\jars\\classes.jar;C:\\Users\\<USER>\\Desktop\\DOAN-THUCTAP\\CKC-Englishoo\\app\\build\\intermediates\\compile_and_runtime_not_namespaced_r_class_jar\\release\\processReleaseResources\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\35df14ffaa25bd540d8ecb1e0809b1d6\\transformed\\android.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 134217728                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 536870912                                 {product} {command line}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 536870912                              {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-20
CLASSPATH=C:\Users\<USER>\Desktop\DOAN-THUCTAP\CKC-Englishoo\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\flutter\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files\Microsoft MPI\Bin\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Azure Data Studio\bin;C:\Program Files (x86)\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Java\jdk-20\bin;C:\kotlinc\bin;D:\Downloads\apache-maven-3.9.9\bin;C:\Program Files\dotnet\;C:\Program Files\Git\cmd;D:\xampp\php;D:\Compose;C:\flutter\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2023.2\bin;;C:\Program Files\Azure Data Studio\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;D:\software\IntelliJ IDEA Community Edition 2024.2.1\bin;;C:\Program Files\Git\bin\git;D:\software\Microsoft VS Code\visualcode\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin
USERNAME=Admin
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 58 Stepping 9, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 4 days 16:46 hours

CPU: total 4 (initial active 4) (2 cores per cpu, 2 threads per core) family 6 model 58 stepping 9 microcode 0x21, cx8, cmov, fxsr, ht, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, tsc, tscinvbit, avx, aes, erms, clmul, vzeroupper, clflush, rdtscp, f16c

Memory: 4k page, system-wide physical 8077M (592M free)
TotalPageFile size 22304M (AvailPageFile size 25M)
current process WorkingSet (physical memory assigned to process): 10M, peak: 10M
current process commit charge ("private bytes"): 51M, peak: 179M

vm_info: Java HotSpot(TM) 64-Bit Server VM (20.0.2*****) for windows-amd64 JRE (20.0.2*****), built on 2023-06-14T10:08:48Z by "mach5one" with MS VC++ 17.1 (VS2022)

END.
